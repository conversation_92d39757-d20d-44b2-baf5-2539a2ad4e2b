; تخصيص مثبت NSIS لمشغل يوتيوب

; إضافة رسالة ترحيب
!macro customWelcomePage
  !insertmacro MUI_PAGE_WELCOME
!macroend

; إضافة صفحة الترخيص
!macro customLicensePage
  !insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!macroend

; تخصيص صفحة التثبيت
!macro customInstallPage
  !insertmacro MUI_PAGE_DIRECTORY
  !insertmacro MUI_PAGE_INSTFILES
!macroend

; إضافة ملفات إضافية
!macro customInstallMode
  ; إنشاء اختصار على سطح المكتب
  CreateShortCut "$DESKTOP\مشغل يوتيوب.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\app\src\assets\icon.ico"
  
  ; إنشاء اختصار في قائمة ابدأ
  CreateDirectory "$SMPROGRAMS\مشغل يوتيوب"
  CreateShortCut "$SMPROGRAMS\مشغل يوتيوب\مشغل يوتيوب.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\app\src\assets\icon.ico"
  CreateShortCut "$SMPROGRAMS\مشغل يوتيوب\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall ${APP_FILENAME}.exe"
  
  ; تسجيل التطبيق في النظام
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayName" "مشغل يوتيوب"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayVersion" "${APP_VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "Publisher" "YouTube Player Developer"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayIcon" "$INSTDIR\resources\app\src\assets\icon.ico"
!macroend

; تنظيف عند إلغاء التثبيت
!macro customUnInstallMode
  ; حذف الاختصارات
  Delete "$DESKTOP\مشغل يوتيوب.lnk"
  Delete "$SMPROGRAMS\مشغل يوتيوب\مشغل يوتيوب.lnk"
  Delete "$SMPROGRAMS\مشغل يوتيوب\إلغاء التثبيت.lnk"
  RMDir "$SMPROGRAMS\مشغل يوتيوب"
  
  ; حذف مفاتيح التسجيل
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}"
!macroend

