import React, { useState, useEffect } from 'react';
import './App.css';
import Header from './components/Header';
import YouTubePlayerEnhanced from './components/YouTubePlayerEnhanced';
import SettingsEnhanced from './components/SettingsEnhanced';
import SidebarEnhanced from './components/SidebarEnhanced';
import DeveloperInfo from './components/DeveloperInfo';
import { YouTubeHelper } from './utils/youtubeApi';
import { adBlocker } from './utils/adBlocker';
import { developerMonetization, integratedAds } from './utils/developerMonetization';
import { channelConfig, loadChannelVideos, recordView } from './utils/channelConfig';

function AppFinal() {
  const [currentVideoId, setCurrentVideoId] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showDeveloperInfo, setShowDeveloperInfo] = useState(false);
  const [channelVideos, setChannelVideos] = useState([]);
  const [channelInfo, setChannelInfo] = useState(null);
  const [isLoadingChannel, setIsLoadingChannel] = useState(true);
  const [settings, setSettings] = useState({
    quality: 'auto',
    hideControls: false,
    adBlock: true,
    darkTheme: true,
    volume: 100,
    autoplay: false
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [appStats, setAppStats] = useState({
    videosWatched: 0,
    adsBlocked: 0,
    timeSpent: 0
  });

  // تحميل الإعدادات من التخزين المحلي
  useEffect(() => {
    const savedSettings = localStorage.getItem('youtube-player-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }

    const savedStats = localStorage.getItem('youtube-player-stats');
    if (savedStats) {
      try {
        const parsed = JSON.parse(savedStats);
        setAppStats(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading stats:', error);
      }
    }

    // تحميل القناة الافتراضية عند بدء التطبيق
    loadDefaultChannel();

    // بدء دوران الإعلانات
    integratedAds.startAdRotation();
  }, []);

  // تحميل القناة الافتراضية
  const loadDefaultChannel = async () => {
    try {
      setIsLoadingChannel(true);
      const channelData = await loadChannelVideos(channelConfig.defaultChannel.channelId);
      
      setChannelInfo(channelData.channelInfo);
      setChannelVideos(channelData.videos);
      
      // تشغيل أول فيديو تلقائياً إذا كان متاحاً
      if (channelData.videos.length > 0 && settings.autoplay) {
        const firstVideo = channelData.videos[0];
        setCurrentVideoId(firstVideo.id);
        recordView(firstVideo.id, channelConfig.defaultChannel.channelId);
      }
    } catch (error) {
      console.error('Error loading default channel:', error);
    } finally {
      setIsLoadingChannel(false);
    }
  };

  // حفظ الإعدادات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('youtube-player-settings', JSON.stringify(settings));
  }, [settings]);

  // حفظ الإحصائيات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('youtube-player-stats', JSON.stringify(appStats));
  }, [appStats]);

  // معالجة البحث أو تشغيل الفيديو
  const handleVideoLoad = (input) => {
    const videoId = YouTubeHelper.extractVideoId(input);
    if (videoId) {
      setCurrentVideoId(videoId);
      setSearchQuery('');
      
      // تحديث إحصائية الفيديوهات المشاهدة
      setAppStats(prev => ({
        ...prev,
        videosWatched: prev.videosWatched + 1
      }));

      // إرسال حدث بدء الفيديو لنظام الربح
      window.dispatchEvent(new CustomEvent('video-started', { detail: { videoId } }));
    } else {
      // إذا لم يكن رابط، فهو استعلام بحث
      setSearchQuery(input);
    }
  };

  // تحديث الإعدادات
  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  // معالجة اختيار فيديو من الشريط الجانبي
  const handleVideoSelect = (videoId) => {
    setCurrentVideoId(videoId);
    setSearchQuery('');
    
    // تحديث إحصائية الفيديوهات المشاهدة
    setAppStats(prev => ({
      ...prev,
      videosWatched: prev.videosWatched + 1
    }));

    // تسجيل المشاهدة للقناة الافتراضية
    recordView(videoId, channelConfig.defaultChannel.channelId);

    // إرسال حدث بدء الفيديو
    window.dispatchEvent(new CustomEvent('video-started', { detail: { videoId } }));
  };

  // محاكاة حجب الإعلانات وتحديث الإحصائيات
  useEffect(() => {
    if (settings.adBlock && currentVideoId) {
      const interval = setInterval(() => {
        // محاكاة حجب إعلانات
        const adsBlocked = Math.floor(Math.random() * 3) + 1;
        
        setAppStats(prev => ({
          ...prev,
          adsBlocked: prev.adsBlocked + adsBlocked
        }));

        // تسجيل الإعلانات المحجوبة في نظام مانع الإعلانات
        for (let i = 0; i < adsBlocked; i++) {
          adBlocker.blockAd(`https://ads.youtube.com/fake-ad-${Date.now()}-${i}`);
        }
      }, 15000); // كل 15 ثانية

      return () => clearInterval(interval);
    }
  }, [settings.adBlock, currentVideoId]);

  // تتبع الوقت المقضي
  useEffect(() => {
    const interval = setInterval(() => {
      setAppStats(prev => ({
        ...prev,
        timeSpent: prev.timeSpent + 1
      }));
    }, 60000); // كل دقيقة

    return () => clearInterval(interval);
  }, []);

  // إضافة الإعلانات المدمجة للشريط الجانبي
  const renderSidebarWithAds = () => {
    return (
      <div className="sidebar-with-ads">
        <SidebarEnhanced 
          searchQuery={searchQuery}
          onVideoSelect={handleVideoSelect}
          currentVideoId={currentVideoId}
          channelVideos={channelVideos}
          channelInfo={channelInfo}
          isLoadingChannel={isLoadingChannel}
        />
        
        {/* إعلان جانبي مدمج */}
        {developerMonetization.adConfig.sidebarAds && (
          <div className="sidebar-ad-container">
            <SidebarAd />
          </div>
        )}
      </div>
    );
  };

  // مكون الإعلان الجانبي
  const SidebarAd = () => {
    const [ad, setAd] = useState(null);

    useEffect(() => {
      const adElement = integratedAds.createSidebarAd();
      if (adElement) {
        setAd(adElement.outerHTML);
      }
    }, []);

    if (!ad) return null;

    return (
      <div 
        className="sidebar-ad"
        dangerouslySetInnerHTML={{ __html: ad }}
      />
    );
  };

  // مكون الإعلان البانر
  const BannerAd = () => {
    const [ad, setAd] = useState(null);

    useEffect(() => {
      const adElement = integratedAds.createBannerAd('banner-container');
      if (adElement) {
        setAd(adElement.outerHTML);
      }
    }, [currentVideoId]); // تغيير الإعلان مع كل فيديو جديد

    if (!ad || !developerMonetization.adConfig.bannerAds) return null;

    return (
      <div 
        className="banner-ad"
        dangerouslySetInnerHTML={{ __html: ad }}
      />
    );
  };

  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}س ${mins}د`;
    }
    return `${mins}د`;
  };

  return (
    <div className={`app ${settings.darkTheme ? 'dark-theme' : 'light-theme'}`}>
      <Header 
        onVideoLoad={handleVideoLoad}
        onSettingsClick={() => setShowSettings(!showSettings)}
        stats={appStats}
      />
      
      {/* إعلان بانر علوي */}
      <BannerAd />
      
      <div className="app-content">
        {renderSidebarWithAds()}
        
        <main className="main-content">
          <YouTubePlayerEnhanced 
            videoId={currentVideoId}
            settings={settings}
          />
          
          {/* شريط الحالة المحسن */}
          {currentVideoId && (
            <div className="status-bar">
              <div className="status-item">
                <span>🎥</span>
                <span>فيديو نشط</span>
              </div>
              
              {settings.adBlock && (
                <div className="status-item">
                  <span>🛡️</span>
                  <span>مانع الإعلانات مفعل</span>
                </div>
              )}
              
              <div className="status-item">
                <span>⚙️</span>
                <span>جودة: {YouTubeHelper.getQualityLabel(settings.quality)}</span>
              </div>
              
              <div className="status-item">
                <span>📊</span>
                <span>{appStats.adsBlocked} إعلان محجوب</span>
              </div>

              <div className="status-item">
                <span>💰</span>
                <span>أرباح المطور: {developerMonetization.getAnalytics().revenueFormatted}</span>
              </div>
            </div>
          )}
        </main>
        
        {showSettings && (
          <SettingsEnhanced 
            settings={settings}
            onSettingsChange={updateSettings}
            onClose={() => setShowSettings(false)}
            stats={appStats}
          />
        )}
      </div>
      
      {/* رسالة ترحيب للمستخدمين الجدد */}
      {appStats.videosWatched === 0 && !currentVideoId && !isLoadingChannel && (
        <div className="welcome-overlay">
          <div className="welcome-content">
            <h2>🎉 مرحباً بك في مشغل يوتيوب المتقدم!</h2>
            <p>استمتع بتجربة مشاهدة محسنة مع:</p>
            <ul>
              <li>🚫 حجب الإعلانات تلقائياً</li>
              <li>🎨 واجهة عربية أنيقة</li>
              <li>⚙️ تحكم كامل في الجودة</li>
              <li>📱 تصميم متجاوب</li>
              <li>💰 دعم المطور بطريقة غير مزعجة</li>
            </ul>
            <div className="welcome-buttons">
              <button 
                className="welcome-btn"
                onClick={() => setAppStats(prev => ({ ...prev, videosWatched: 1 }))}
              >
                ابدأ الآن
              </button>
              <button 
                className="developer-btn"
                onClick={() => setShowDeveloperInfo(true)}
              >
                معلومات المطور
              </button>
            </div>
          </div>
        </div>
      )}

      {/* إشعار حجب الإعلانات */}
      {settings.adBlock && currentVideoId && (
        <div className="ad-block-notification">
          <span>🛡️ تم حجب {appStats.adsBlocked} إعلان</span>
        </div>
      )}

      {/* معلومات المطور */}
      {showDeveloperInfo && (
        <DeveloperInfo onClose={() => setShowDeveloperInfo(false)} />
      )}

      {/* معلومات المطور المدمجة */}
      <DeveloperInfo compact={true} />
    </div>
  );
}

export default AppFinal;

