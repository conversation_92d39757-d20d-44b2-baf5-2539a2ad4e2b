#!/usr/bin/env python3
from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    # إنشاء أيقونة 256x256
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # خلفية دائرية حمراء (لون يوتيوب)
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(255, 0, 0, 255), outline=(200, 0, 0, 255), width=3)
    
    # رمز التشغيل (مثلث أبيض)
    triangle_size = 60
    center_x, center_y = size // 2, size // 2
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    draw.polygon(triangle_points, fill=(255, 255, 255, 255))
    
    # حفظ بأحجام مختلفة
    sizes = [16, 32, 48, 64, 128, 256]
    
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        resized.save(f'public/icons/icon-{icon_size}.png')
        resized.save(f'src/assets/icon-{icon_size}.png')
    
    # حفظ كـ ICO للويندوز
    img.save('src/assets/icon.ico', format='ICO', sizes=[(256, 256)])
    img.save('public/favicon.ico', format='ICO', sizes=[(32, 32)])
    
    print("تم إنشاء الأيقونات بنجاح!")

if __name__ == "__main__":
    create_app_icon()

