{"ast": null, "code": "import React,{useState,useEffect}from'react';import{YouTubeHelper}from'../utils/youtubeApi';import{channelConfig}from'../utils/channelConfig';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SidebarEnhanced=_ref=>{let{searchQuery,onVideoSelect,currentVideoId,channelVideos=[],channelInfo=null,isLoadingChannel=false}=_ref;const[searchResults,setSearchResults]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[activeTab,setActiveTab]=useState('channel');// فيديوهات مقترحة افتراضية للبحث\nconst defaultSearchVideos=[{id:'dQw4w9WgXcQ',title:'<PERSON> - Never Gonna Give You Up (Official Video)',channel:'<PERSON>',duration:'3:33',views:'1.4B',publishedAt:'2009-10-25',thumbnail:'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'},{id:'kJQP7kiw5Fk',title:'Luis Fonsi - Despacito ft. Daddy Yankee',channel:'Luis Fonsi',duration:'4:42',views:'8.2B',publishedAt:'2017-01-12',thumbnail:'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'},{id:'fJ9rUzIMcZQ',title:'Queen – Bohemian Rhapsody (Official Video Remastered)',channel:'Queen Official',duration:'5:55',views:'1.9B',publishedAt:'2008-08-01',thumbnail:'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'}];useEffect(()=>{if(searchQuery&&searchQuery.trim()){setActiveTab('search');performSearch(searchQuery);}else{setActiveTab('channel');setSearchResults([]);}},[searchQuery]);const performSearch=async query=>{setLoading(true);setError(null);try{const results=await YouTubeHelper.searchVideos(query,20);setSearchResults(results.length>0?results:defaultSearchVideos);}catch(error){console.error('خطأ في البحث:',error);setError('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');setSearchResults(defaultSearchVideos);}finally{setLoading(false);}};const handleVideoClick=videoId=>{onVideoSelect(videoId);};const formatViews=views=>{if(typeof views==='string')return views;if(views>=1000000000){return(views/1000000000).toFixed(1)+'B';}else if(views>=1000000){return(views/1000000).toFixed(1)+'M';}else if(views>=1000){return(views/1000).toFixed(1)+'K';}return views.toString();};const formatPublishedDate=dateString=>{const date=new Date(dateString);const now=new Date();const diffTime=Math.abs(now-date);const diffDays=Math.ceil(diffTime/(1000*60*60*24));if(diffDays<7){return`منذ ${diffDays} أيام`;}else if(diffDays<30){return`منذ ${Math.ceil(diffDays/7)} أسابيع`;}else if(diffDays<365){return`منذ ${Math.ceil(diffDays/30)} أشهر`;}else{return`منذ ${Math.ceil(diffDays/365)} سنوات`;}};// عرض معلومات القناة\nconst renderChannelHeader=()=>{if(!channelInfo)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"channel-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"channel-avatar\",children:/*#__PURE__*/_jsx(\"div\",{className:\"avatar-placeholder\",children:channelInfo.name.charAt(0)})}),/*#__PURE__*/_jsxs(\"div\",{className:\"channel-info\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"channel-name\",children:channelInfo.name}),/*#__PURE__*/_jsxs(\"p\",{className:\"channel-subscribers\",children:[channelInfo.subscribers,\" \\u0645\\u0634\\u062A\\u0631\\u0643\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"channel-description\",children:channelInfo.description})]}),/*#__PURE__*/_jsx(\"button\",{className:\"subscribe-btn\",onClick:()=>window.open(channelConfig.developer.socialLinks.youtube,'_blank'),children:\"\\u0627\\u0634\\u062A\\u0631\\u0627\\u0643\"})]});};// عرض قائمة الفيديوهات\nconst renderVideoList=function(videoList){let isChannelVideos=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;if(videoList.length===0){return/*#__PURE__*/_jsx(\"div\",{className:\"no-videos\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0645\\u062A\\u0627\\u062D\\u0629\"})});}return/*#__PURE__*/_jsx(\"div\",{className:\"video-list\",children:videoList.map(video=>/*#__PURE__*/_jsxs(\"div\",{className:`video-item ${currentVideoId===video.id?'active':''}`,onClick:()=>handleVideoClick(video.id),children:[/*#__PURE__*/_jsxs(\"div\",{style:{position:'relative'},children:[/*#__PURE__*/_jsx(\"img\",{src:video.thumbnail||'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+',alt:video.title,className:\"video-thumbnail\",onError:e=>{e.target.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+';}}),video.duration&&/*#__PURE__*/_jsx(\"div\",{className:\"video-duration\",children:video.duration})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"video-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"video-title\",title:video.title,children:video.title}),/*#__PURE__*/_jsx(\"div\",{className:\"video-channel\",children:isChannelVideos?channelInfo===null||channelInfo===void 0?void 0:channelInfo.name:video.channel||video.channelName}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',marginTop:'5px'},children:[video.views&&/*#__PURE__*/_jsxs(\"div\",{className:\"video-views\",children:[\"\\uD83D\\uDC41\\uFE0F \",formatViews(video.views),\" \\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"]}),(video.publishedAt||video.uploadDate)&&/*#__PURE__*/_jsx(\"div\",{className:\"video-published\",children:formatPublishedDate(video.publishedAt||video.uploadDate)})]})]})]},video.id))});};return/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-tabs\",children:[/*#__PURE__*/_jsxs(\"button\",{className:`tab-btn ${activeTab==='channel'?'active':''}`,onClick:()=>setActiveTab('channel'),children:[\"\\uD83C\\uDFE0 \",channelConfig.developer.channelName]}),searchQuery&&/*#__PURE__*/_jsx(\"button\",{className:`tab-btn ${activeTab==='search'?'active':''}`,onClick:()=>setActiveTab('search'),children:\"\\uD83D\\uDD0D \\u0627\\u0644\\u0628\\u062D\\u062B\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-content\",children:[activeTab==='channel'&&/*#__PURE__*/_jsxs(\"div\",{className:\"channel-tab\",children:[renderChannelHeader(),isLoadingChannel?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'30px',color:'#b3b3b3'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{style:{marginTop:'10px'},children:\"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0627\\u0644\\u0642\\u0646\\u0627\\u0629...\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-title\",children:/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0623\\u062D\\u062F\\u062B \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A\"})}),renderVideoList(channelVideos,true)]})]}),activeTab==='search'&&/*#__PURE__*/_jsxs(\"div\",{className:\"search-tab\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"sidebar-title\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u0646\\u062A\\u0627\\u0626\\u062C \\u0627\\u0644\\u0628\\u062D\\u062B: \",searchQuery]})]}),searchResults.length>0&&!loading&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'var(--secondary-text)',marginTop:'5px'},children:[searchResults.length,\" \\u0646\\u062A\\u064A\\u062C\\u0629\"]})]}),loading?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'30px',color:'#b3b3b3'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{style:{marginTop:'10px'},children:\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u0628\\u062D\\u062B...\"})]}):error?/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"p\",{children:error}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>performSearch(searchQuery),style:{background:'none',border:'1px solid white',color:'white',padding:'5px 10px',borderRadius:'3px',marginTop:'10px',cursor:'pointer'},children:\"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629\"})]}):renderVideoList(searchResults,false)]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-footer\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"developer-credit\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u062A\\u0645 \\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0628\\u0648\\u0627\\u0633\\u0637\\u0629:\"}),/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:channelConfig.developer.name})}),/*#__PURE__*/_jsx(\"p\",{children:channelConfig.developer.phone}),/*#__PURE__*/_jsx(\"p\",{children:channelConfig.developer.channelName})]})})]});};export default SidebarEnhanced;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "YouTubeHelper", "channelConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SidebarEnhanced", "_ref", "searchQuery", "onVideoSelect", "currentVideoId", "channelVideos", "channelInfo", "isLoadingChannel", "searchResults", "setSearchResults", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "defaultSearchVideos", "id", "title", "channel", "duration", "views", "publishedAt", "thumbnail", "trim", "performSearch", "query", "results", "searchVideos", "length", "console", "handleVideoClick", "videoId", "formatViews", "toFixed", "toString", "formatPublishedDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "renderChannelHeader", "className", "children", "name", "char<PERSON>t", "subscribers", "description", "onClick", "window", "open", "developer", "socialLinks", "youtube", "renderVideoList", "videoList", "isChannelVideos", "arguments", "undefined", "map", "video", "style", "position", "src", "alt", "onError", "e", "target", "channelName", "display", "justifyContent", "marginTop", "uploadDate", "textAlign", "padding", "color", "fontSize", "background", "border", "borderRadius", "cursor", "phone"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/components/SidebarEnhanced.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { YouTubeHelper } from '../utils/youtubeApi';\nimport { channelConfig } from '../utils/channelConfig';\n\nconst SidebarEnhanced = ({ \n  searchQuery, \n  onVideoSelect, \n  currentVideoId,\n  channelVideos = [],\n  channelInfo = null,\n  isLoadingChannel = false\n}) => {\n  const [searchResults, setSearchResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState('channel');\n\n  // فيديوهات مقترحة افتراضية للبحث\n  const defaultSearchVideos = [\n    {\n      id: 'dQw4w9WgXcQ',\n      title: '<PERSON> - Never Gonna Give You Up (Official Video)',\n      channel: '<PERSON>',\n      duration: '3:33',\n      views: '1.4B',\n      publishedAt: '2009-10-25',\n      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'\n    },\n    {\n      id: 'kJQP7kiw5Fk',\n      title: '<PERSON> Fonsi - Despacito ft. Daddy Yankee',\n      channel: 'Luis Fonsi',\n      duration: '4:42',\n      views: '8.2B',\n      publishedAt: '2017-01-12',\n      thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'\n    },\n    {\n      id: 'fJ9rUzIMcZQ',\n      title: 'Queen – Bohemian Rhapsody (Official Video Remastered)',\n      channel: 'Queen Official',\n      duration: '5:55',\n      views: '1.9B',\n      publishedAt: '2008-08-01',\n      thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'\n    }\n  ];\n\n  useEffect(() => {\n    if (searchQuery && searchQuery.trim()) {\n      setActiveTab('search');\n      performSearch(searchQuery);\n    } else {\n      setActiveTab('channel');\n      setSearchResults([]);\n    }\n  }, [searchQuery]);\n\n  const performSearch = async (query) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const results = await YouTubeHelper.searchVideos(query, 20);\n      setSearchResults(results.length > 0 ? results : defaultSearchVideos);\n    } catch (error) {\n      console.error('خطأ في البحث:', error);\n      setError('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');\n      setSearchResults(defaultSearchVideos);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleVideoClick = (videoId) => {\n    onVideoSelect(videoId);\n  };\n\n  const formatViews = (views) => {\n    if (typeof views === 'string') return views;\n    \n    if (views >= 1000000000) {\n      return (views / 1000000000).toFixed(1) + 'B';\n    } else if (views >= 1000000) {\n      return (views / 1000000).toFixed(1) + 'M';\n    } else if (views >= 1000) {\n      return (views / 1000).toFixed(1) + 'K';\n    }\n    return views.toString();\n  };\n\n  const formatPublishedDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays < 7) {\n      return `منذ ${diffDays} أيام`;\n    } else if (diffDays < 30) {\n      return `منذ ${Math.ceil(diffDays / 7)} أسابيع`;\n    } else if (diffDays < 365) {\n      return `منذ ${Math.ceil(diffDays / 30)} أشهر`;\n    } else {\n      return `منذ ${Math.ceil(diffDays / 365)} سنوات`;\n    }\n  };\n\n  // عرض معلومات القناة\n  const renderChannelHeader = () => {\n    if (!channelInfo) return null;\n\n    return (\n      <div className=\"channel-header\">\n        <div className=\"channel-avatar\">\n          <div className=\"avatar-placeholder\">\n            {channelInfo.name.charAt(0)}\n          </div>\n        </div>\n        <div className=\"channel-info\">\n          <h3 className=\"channel-name\">{channelInfo.name}</h3>\n          <p className=\"channel-subscribers\">{channelInfo.subscribers} مشترك</p>\n          <p className=\"channel-description\">{channelInfo.description}</p>\n        </div>\n        <button \n          className=\"subscribe-btn\"\n          onClick={() => window.open(channelConfig.developer.socialLinks.youtube, '_blank')}\n        >\n          اشتراك\n        </button>\n      </div>\n    );\n  };\n\n  // عرض قائمة الفيديوهات\n  const renderVideoList = (videoList, isChannelVideos = false) => {\n    if (videoList.length === 0) {\n      return (\n        <div className=\"no-videos\">\n          <p>لا توجد فيديوهات متاحة</p>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"video-list\">\n        {videoList.map((video) => (\n          <div\n            key={video.id}\n            className={`video-item ${currentVideoId === video.id ? 'active' : ''}`}\n            onClick={() => handleVideoClick(video.id)}\n          >\n            <div style={{ position: 'relative' }}>\n              <img \n                src={video.thumbnail || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+'}\n                alt={video.title}\n                className=\"video-thumbnail\"\n                onError={(e) => {\n                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+';\n                }}\n              />\n              {video.duration && (\n                <div className=\"video-duration\">\n                  {video.duration}\n                </div>\n              )}\n            </div>\n            \n            <div className=\"video-info\">\n              <div className=\"video-title\" title={video.title}>\n                {video.title}\n              </div>\n              <div className=\"video-channel\">\n                {isChannelVideos ? channelInfo?.name : (video.channel || video.channelName)}\n              </div>\n              \n              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '5px' }}>\n                {video.views && (\n                  <div className=\"video-views\">\n                    👁️ {formatViews(video.views)} مشاهدة\n                  </div>\n                )}\n                {(video.publishedAt || video.uploadDate) && (\n                  <div className=\"video-published\">\n                    {formatPublishedDate(video.publishedAt || video.uploadDate)}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"sidebar\">\n      {/* تبويبات */}\n      <div className=\"sidebar-tabs\">\n        <button \n          className={`tab-btn ${activeTab === 'channel' ? 'active' : ''}`}\n          onClick={() => setActiveTab('channel')}\n        >\n          🏠 {channelConfig.developer.channelName}\n        </button>\n        {searchQuery && (\n          <button \n            className={`tab-btn ${activeTab === 'search' ? 'active' : ''}`}\n            onClick={() => setActiveTab('search')}\n          >\n            🔍 البحث\n          </button>\n        )}\n      </div>\n\n      {/* محتوى الشريط الجانبي */}\n      <div className=\"sidebar-content\">\n        {activeTab === 'channel' && (\n          <div className=\"channel-tab\">\n            {renderChannelHeader()}\n            \n            {isLoadingChannel ? (\n              <div style={{ textAlign: 'center', padding: '30px', color: '#b3b3b3' }}>\n                <div className=\"loading-spinner\"></div>\n                <p style={{ marginTop: '10px' }}>جاري تحميل فيديوهات القناة...</p>\n              </div>\n            ) : (\n              <>\n                <div className=\"section-title\">\n                  <h4>أحدث الفيديوهات</h4>\n                </div>\n                {renderVideoList(channelVideos, true)}\n              </>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'search' && (\n          <div className=\"search-tab\">\n            <div className=\"sidebar-header\">\n              <h3 className=\"sidebar-title\">\n                <span>🔍</span>\n                <span>نتائج البحث: {searchQuery}</span>\n              </h3>\n              \n              {searchResults.length > 0 && !loading && (\n                <div style={{ fontSize: '12px', color: 'var(--secondary-text)', marginTop: '5px' }}>\n                  {searchResults.length} نتيجة\n                </div>\n              )}\n            </div>\n            \n            {loading ? (\n              <div style={{ textAlign: 'center', padding: '30px', color: '#b3b3b3' }}>\n                <div className=\"loading-spinner\"></div>\n                <p style={{ marginTop: '10px' }}>جاري البحث...</p>\n              </div>\n            ) : error ? (\n              <div className=\"error-message\">\n                <p>{error}</p>\n                <button \n                  onClick={() => performSearch(searchQuery)}\n                  style={{ \n                    background: 'none', \n                    border: '1px solid white', \n                    color: 'white', \n                    padding: '5px 10px', \n                    borderRadius: '3px', \n                    marginTop: '10px',\n                    cursor: 'pointer'\n                  }}\n                >\n                  إعادة المحاولة\n                </button>\n              </div>\n            ) : (\n              renderVideoList(searchResults, false)\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* معلومات المطور */}\n      <div className=\"sidebar-footer\">\n        <div className=\"developer-credit\">\n          <p>تم التطوير بواسطة:</p>\n          <p><strong>{channelConfig.developer.name}</strong></p>\n          <p>{channelConfig.developer.phone}</p>\n          <p>{channelConfig.developer.channelName}</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SidebarEnhanced;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,aAAa,KAAQ,qBAAqB,CACnD,OAASC,aAAa,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvD,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAOlB,IAPmB,CACvBC,WAAW,CACXC,aAAa,CACbC,cAAc,CACdC,aAAa,CAAG,EAAE,CAClBC,WAAW,CAAG,IAAI,CAClBC,gBAAgB,CAAG,KACrB,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,aAAa,CAAEC,gBAAgB,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACwB,SAAS,CAAEC,YAAY,CAAC,CAAGzB,QAAQ,CAAC,SAAS,CAAC,CAErD;AACA,KAAM,CAAA0B,mBAAmB,CAAG,CAC1B,CACEC,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,wDAAwD,CAC/DC,OAAO,CAAE,aAAa,CACtBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YAAY,CACzBC,SAAS,CAAE,sDACb,CAAC,CACD,CACEN,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,yCAAyC,CAChDC,OAAO,CAAE,YAAY,CACrBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YAAY,CACzBC,SAAS,CAAE,sDACb,CAAC,CACD,CACEN,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,uDAAuD,CAC9DC,OAAO,CAAE,gBAAgB,CACzBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YAAY,CACzBC,SAAS,CAAE,sDACb,CAAC,CACF,CAEDhC,SAAS,CAAC,IAAM,CACd,GAAIW,WAAW,EAAIA,WAAW,CAACsB,IAAI,CAAC,CAAC,CAAE,CACrCT,YAAY,CAAC,QAAQ,CAAC,CACtBU,aAAa,CAACvB,WAAW,CAAC,CAC5B,CAAC,IAAM,CACLa,YAAY,CAAC,SAAS,CAAC,CACvBN,gBAAgB,CAAC,EAAE,CAAC,CACtB,CACF,CAAC,CAAE,CAACP,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAAuB,aAAa,CAAG,KAAO,CAAAC,KAAK,EAAK,CACrCf,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAc,OAAO,CAAG,KAAM,CAAAnC,aAAa,CAACoC,YAAY,CAACF,KAAK,CAAE,EAAE,CAAC,CAC3DjB,gBAAgB,CAACkB,OAAO,CAACE,MAAM,CAAG,CAAC,CAAGF,OAAO,CAAGX,mBAAmB,CAAC,CACtE,CAAE,MAAOJ,KAAK,CAAE,CACdkB,OAAO,CAAClB,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCC,QAAQ,CAAC,8CAA8C,CAAC,CACxDJ,gBAAgB,CAACO,mBAAmB,CAAC,CACvC,CAAC,OAAS,CACRL,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoB,gBAAgB,CAAIC,OAAO,EAAK,CACpC7B,aAAa,CAAC6B,OAAO,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIZ,KAAK,EAAK,CAC7B,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,MAAO,CAAAA,KAAK,CAE3C,GAAIA,KAAK,EAAI,UAAU,CAAE,CACvB,MAAO,CAACA,KAAK,CAAG,UAAU,EAAEa,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAC9C,CAAC,IAAM,IAAIb,KAAK,EAAI,OAAO,CAAE,CAC3B,MAAO,CAACA,KAAK,CAAG,OAAO,EAAEa,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAC3C,CAAC,IAAM,IAAIb,KAAK,EAAI,IAAI,CAAE,CACxB,MAAO,CAACA,KAAK,CAAG,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CACxC,CACA,MAAO,CAAAb,KAAK,CAACc,QAAQ,CAAC,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIC,UAAU,EAAK,CAC1C,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,KAAM,CAAAG,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAE,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAAGF,IAAI,CAAC,CACrC,KAAM,CAAAM,QAAQ,CAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAE5D,GAAIG,QAAQ,CAAG,CAAC,CAAE,CAChB,MAAO,OAAOA,QAAQ,OAAO,CAC/B,CAAC,IAAM,IAAIA,QAAQ,CAAG,EAAE,CAAE,CACxB,MAAO,OAAOF,IAAI,CAACG,IAAI,CAACD,QAAQ,CAAG,CAAC,CAAC,SAAS,CAChD,CAAC,IAAM,IAAIA,QAAQ,CAAG,GAAG,CAAE,CACzB,MAAO,OAAOF,IAAI,CAACG,IAAI,CAACD,QAAQ,CAAG,EAAE,CAAC,OAAO,CAC/C,CAAC,IAAM,CACL,MAAO,OAAOF,IAAI,CAACG,IAAI,CAACD,QAAQ,CAAG,GAAG,CAAC,QAAQ,CACjD,CACF,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAACxC,WAAW,CAAE,MAAO,KAAI,CAE7B,mBACET,KAAA,QAAKkD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BrD,IAAA,QAAKoD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BrD,IAAA,QAAKoD,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChC1C,WAAW,CAAC2C,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,cACNrD,KAAA,QAAKkD,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BrD,IAAA,OAAIoD,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAE1C,WAAW,CAAC2C,IAAI,CAAK,CAAC,cACpDpD,KAAA,MAAGkD,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAE1C,WAAW,CAAC6C,WAAW,CAAC,iCAAM,EAAG,CAAC,cACtExD,IAAA,MAAGoD,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE1C,WAAW,CAAC8C,WAAW,CAAI,CAAC,EAC7D,CAAC,cACNzD,IAAA,WACEoD,SAAS,CAAC,eAAe,CACzBM,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,IAAI,CAAC9D,aAAa,CAAC+D,SAAS,CAACC,WAAW,CAACC,OAAO,CAAE,QAAQ,CAAE,CAAAV,QAAA,CACnF,sCAED,CAAQ,CAAC,EACN,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAW,eAAe,CAAG,QAAAA,CAACC,SAAS,CAA8B,IAA5B,CAAAC,eAAe,CAAAC,SAAA,CAAAjC,MAAA,IAAAiC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CACzD,GAAIF,SAAS,CAAC/B,MAAM,GAAK,CAAC,CAAE,CAC1B,mBACElC,IAAA,QAAKoD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBrD,IAAA,MAAAqD,QAAA,CAAG,uHAAsB,CAAG,CAAC,CAC1B,CAAC,CAEV,CAEA,mBACErD,IAAA,QAAKoD,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBY,SAAS,CAACI,GAAG,CAAEC,KAAK,eACnBpE,KAAA,QAEEkD,SAAS,CAAE,cAAc3C,cAAc,GAAK6D,KAAK,CAAChD,EAAE,CAAG,QAAQ,CAAG,EAAE,EAAG,CACvEoC,OAAO,CAAEA,CAAA,GAAMtB,gBAAgB,CAACkC,KAAK,CAAChD,EAAE,CAAE,CAAA+B,QAAA,eAE1CnD,KAAA,QAAKqE,KAAK,CAAE,CAAEC,QAAQ,CAAE,UAAW,CAAE,CAAAnB,QAAA,eACnCrD,IAAA,QACEyE,GAAG,CAAEH,KAAK,CAAC1C,SAAS,EAAI,oSAAqS,CAC7T8C,GAAG,CAAEJ,KAAK,CAAC/C,KAAM,CACjB6B,SAAS,CAAC,iBAAiB,CAC3BuB,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACJ,GAAG,CAAG,oSAAoS,CACrT,CAAE,CACH,CAAC,CACDH,KAAK,CAAC7C,QAAQ,eACbzB,IAAA,QAAKoD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5BiB,KAAK,CAAC7C,QAAQ,CACZ,CACN,EACE,CAAC,cAENvB,KAAA,QAAKkD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrD,IAAA,QAAKoD,SAAS,CAAC,aAAa,CAAC7B,KAAK,CAAE+C,KAAK,CAAC/C,KAAM,CAAA8B,QAAA,CAC7CiB,KAAK,CAAC/C,KAAK,CACT,CAAC,cACNvB,IAAA,QAAKoD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3Ba,eAAe,CAAGvD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE2C,IAAI,CAAIgB,KAAK,CAAC9C,OAAO,EAAI8C,KAAK,CAACQ,WAAY,CACxE,CAAC,cAEN5E,KAAA,QAAKqE,KAAK,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,SAAS,CAAE,KAAM,CAAE,CAAA5B,QAAA,EAChFiB,KAAK,CAAC5C,KAAK,eACVxB,KAAA,QAAKkD,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,qBACvB,CAACf,WAAW,CAACgC,KAAK,CAAC5C,KAAK,CAAC,CAAC,uCAChC,EAAK,CACN,CACA,CAAC4C,KAAK,CAAC3C,WAAW,EAAI2C,KAAK,CAACY,UAAU,gBACrClF,IAAA,QAAKoD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BZ,mBAAmB,CAAC6B,KAAK,CAAC3C,WAAW,EAAI2C,KAAK,CAACY,UAAU,CAAC,CACxD,CACN,EACE,CAAC,EACH,CAAC,GAxCDZ,KAAK,CAAChD,EAyCR,CACN,CAAC,CACC,CAAC,CAEV,CAAC,CAED,mBACEpB,KAAA,QAAKkD,SAAS,CAAC,SAAS,CAAAC,QAAA,eAEtBnD,KAAA,QAAKkD,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnD,KAAA,WACEkD,SAAS,CAAE,WAAWjC,SAAS,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,EAAG,CAChEuC,OAAO,CAAEA,CAAA,GAAMtC,YAAY,CAAC,SAAS,CAAE,CAAAiC,QAAA,EACxC,eACI,CAACvD,aAAa,CAAC+D,SAAS,CAACiB,WAAW,EACjC,CAAC,CACRvE,WAAW,eACVP,IAAA,WACEoD,SAAS,CAAE,WAAWjC,SAAS,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC/DuC,OAAO,CAAEA,CAAA,GAAMtC,YAAY,CAAC,QAAQ,CAAE,CAAAiC,QAAA,CACvC,6CAED,CAAQ,CACT,EACE,CAAC,cAGNnD,KAAA,QAAKkD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7BlC,SAAS,GAAK,SAAS,eACtBjB,KAAA,QAAKkD,SAAS,CAAC,aAAa,CAAAC,QAAA,EACzBF,mBAAmB,CAAC,CAAC,CAErBvC,gBAAgB,cACfV,KAAA,QAAKqE,KAAK,CAAE,CAAEY,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAhC,QAAA,eACrErD,IAAA,QAAKoD,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCpD,IAAA,MAAGuE,KAAK,CAAE,CAAEU,SAAS,CAAE,MAAO,CAAE,CAAA5B,QAAA,CAAC,kJAA6B,CAAG,CAAC,EAC/D,CAAC,cAENnD,KAAA,CAAAE,SAAA,EAAAiD,QAAA,eACErD,IAAA,QAAKoD,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BrD,IAAA,OAAAqD,QAAA,CAAI,uFAAe,CAAI,CAAC,CACrB,CAAC,CACLW,eAAe,CAACtD,aAAa,CAAE,IAAI,CAAC,EACrC,CACH,EACE,CACN,CAEAS,SAAS,GAAK,QAAQ,eACrBjB,KAAA,QAAKkD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnD,KAAA,QAAKkD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnD,KAAA,OAAIkD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC3BrD,IAAA,SAAAqD,QAAA,CAAM,cAAE,CAAM,CAAC,cACfnD,KAAA,SAAAmD,QAAA,EAAM,iEAAa,CAAC9C,WAAW,EAAO,CAAC,EACrC,CAAC,CAEJM,aAAa,CAACqB,MAAM,CAAG,CAAC,EAAI,CAACnB,OAAO,eACnCb,KAAA,QAAKqE,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,uBAAuB,CAAEJ,SAAS,CAAE,KAAM,CAAE,CAAA5B,QAAA,EAChFxC,aAAa,CAACqB,MAAM,CAAC,iCACxB,EAAK,CACN,EACE,CAAC,CAELnB,OAAO,cACNb,KAAA,QAAKqE,KAAK,CAAE,CAAEY,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAhC,QAAA,eACrErD,IAAA,QAAKoD,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCpD,IAAA,MAAGuE,KAAK,CAAE,CAAEU,SAAS,CAAE,MAAO,CAAE,CAAA5B,QAAA,CAAC,4DAAa,CAAG,CAAC,EAC/C,CAAC,CACJpC,KAAK,cACPf,KAAA,QAAKkD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BrD,IAAA,MAAAqD,QAAA,CAAIpC,KAAK,CAAI,CAAC,cACdjB,IAAA,WACE0D,OAAO,CAAEA,CAAA,GAAM5B,aAAa,CAACvB,WAAW,CAAE,CAC1CgE,KAAK,CAAE,CACLgB,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,iBAAiB,CACzBH,KAAK,CAAE,OAAO,CACdD,OAAO,CAAE,UAAU,CACnBK,YAAY,CAAE,KAAK,CACnBR,SAAS,CAAE,MAAM,CACjBS,MAAM,CAAE,SACV,CAAE,CAAArC,QAAA,CACH,iFAED,CAAQ,CAAC,EACN,CAAC,CAENW,eAAe,CAACnD,aAAa,CAAE,KAAK,CACrC,EACE,CACN,EACE,CAAC,cAGNb,IAAA,QAAKoD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BnD,KAAA,QAAKkD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BrD,IAAA,MAAAqD,QAAA,CAAG,+FAAkB,CAAG,CAAC,cACzBrD,IAAA,MAAAqD,QAAA,cAAGrD,IAAA,WAAAqD,QAAA,CAASvD,aAAa,CAAC+D,SAAS,CAACP,IAAI,CAAS,CAAC,CAAG,CAAC,cACtDtD,IAAA,MAAAqD,QAAA,CAAIvD,aAAa,CAAC+D,SAAS,CAAC8B,KAAK,CAAI,CAAC,cACtC3F,IAAA,MAAAqD,QAAA,CAAIvD,aAAa,CAAC+D,SAAS,CAACiB,WAAW,CAAI,CAAC,EACzC,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}