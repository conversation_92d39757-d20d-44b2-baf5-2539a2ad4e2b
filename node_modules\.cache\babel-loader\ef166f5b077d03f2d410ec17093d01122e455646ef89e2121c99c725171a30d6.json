{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import AppFinal from'./AppFinal';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(AppFinal,{})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "AppFinal", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport AppFinal from './AppFinal';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <AppFinal />\n  </React.StrictMode>\n);\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,QAAQ,KAAM,YAAY,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElC,KAAM,CAAAC,IAAI,CAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACJ,KAAK,CAACU,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACF,QAAQ,GAAE,CAAC,CACI,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}