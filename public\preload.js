// ملف preload.js لتحسين تجربة يوتيوب
const { contextBridge, ipcRenderer } = require('electron');

// انتظار تحميل الصفحة
window.addEventListener('DOMContentLoaded', () => {
  console.log('🎯 مشغل يوتيوب المحسن - تم التحميل');
  
  // حقن CSS مخصص لتحسين المظهر
  injectCustomCSS();
  
  // إزالة العناصر غير المرغوبة
  removeUnwantedElements();
  
  // إضافة مراقب للتغييرات في DOM
  observePageChanges();
  
  // إضافة اختصارات لوحة المفاتيح
  addKeyboardShortcuts();
});

// حقن CSS مخصص
function injectCustomCSS() {
  const style = document.createElement('style');
  style.textContent = `
    /* إخفاء الإعلانات والعناصر المشتتة */
    ytd-display-ad-renderer,
    ytd-video-masthead-ad-renderer,
    ytd-promoted-sparkles-web-renderer,
    .ytd-promoted-video-renderer,
    ytd-ad-slot-renderer,
    yt-mealbar-promo-renderer,
    ytd-statement-banner-renderer,
    ytd-merch-shelf-renderer,
    .ytd-banner-promo-renderer,
    #masthead-ad,
    .video-ads,
    .ytp-ad-module,
    .ytp-ad-overlay-container,
    .ytp-ad-text-overlay,
    .ytp-ad-player-overlay,
    .ytp-ad-skip-button-container,
    .ytp-ad-overlay-close-button,
    .ad-container,
    .advertisement,
    [class*="ad-"],
    [id*="ad-"],
    [class*="ads-"],
    [id*="ads-"] {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      height: 0 !important;
      width: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    
    /* تحسين المظهر العام */
    html {
      background-color: #0f0f0f !important;
    }
    
    /* إخفاء العناصر المشتتة الإضافية */
    ytd-popup-container,
    tp-yt-paper-dialog,
    #dialog,
    .ytd-popup-container,
    ytd-consent-bump-v2-lightbox,
    ytd-single-column-browse-results-renderer > #primary > #contents > ytd-rich-section-renderer,
    ytd-rich-shelf-renderer[is-shorts],
    #shorts-shelf,
    ytd-reel-shelf-renderer {
      display: none !important;
    }
    
    /* تحسين مشغل الفيديو */
    .html5-video-player {
      background-color: #000 !important;
    }
    
    /* إزالة التشويش البصري */
    .ytp-gradient-top,
    .ytp-gradient-bottom {
      display: none !important;
    }
    
    /* تحسين الشريط الجانبي */
    #secondary {
      background-color: #0f0f0f !important;
    }
    
    /* إخفاء اقتراحات الفيديوهات القصيرة */
    ytd-rich-section-renderer:has(ytd-rich-shelf-renderer[is-shorts]) {
      display: none !important;
    }
  `;
  
  document.head.appendChild(style);
  console.log('✅ تم حقن CSS المخصص');
}

// إزالة العناصر غير المرغوبة
function removeUnwantedElements() {
  const unwantedSelectors = [
    'ytd-display-ad-renderer',
    'ytd-video-masthead-ad-renderer',
    'ytd-promoted-sparkles-web-renderer',
    '.ytd-promoted-video-renderer',
    'ytd-ad-slot-renderer',
    'yt-mealbar-promo-renderer',
    'ytd-statement-banner-renderer',
    'ytd-merch-shelf-renderer',
    '.ytd-banner-promo-renderer',
    '#masthead-ad',
    '.video-ads',
    '.ytp-ad-module',
    'ytd-popup-container',
    'tp-yt-paper-dialog',
    '#dialog.ytd-popup-container'
  ];
  
  unwantedSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      element.remove();
    });
  });
  
  console.log('🗑️ تم إزالة العناصر غير المرغوبة');
}

// مراقبة التغييرات في DOM
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // إزالة الإعلانات الجديدة
            if (node.matches && (
              node.matches('ytd-display-ad-renderer') ||
              node.matches('ytd-video-masthead-ad-renderer') ||
              node.matches('ytd-ad-slot-renderer') ||
              node.matches('.ytp-ad-module') ||
              node.matches('ytd-popup-container')
            )) {
              node.remove();
            }
            
            // البحث عن إعلانات داخل العقد الجديدة
            const ads = node.querySelectorAll && node.querySelectorAll(
              'ytd-display-ad-renderer, ytd-video-masthead-ad-renderer, ytd-ad-slot-renderer, .ytp-ad-module, ytd-popup-container'
            );
            if (ads) {
              ads.forEach(ad => ad.remove());
            }
          }
        });
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  console.log('👁️ تم تفعيل مراقب DOM');
}

// إضافة اختصارات لوحة المفاتيح
function addKeyboardShortcuts() {
  document.addEventListener('keydown', (event) => {
    // F11 للشاشة الكاملة
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
    
    // Ctrl+Shift+I لفتح أدوات المطور
    if (event.ctrlKey && event.shiftKey && event.key === 'I') {
      event.preventDefault();
      // سيتم التعامل معه في العملية الرئيسية
    }
  });
  
  console.log('⌨️ تم تفعيل اختصارات لوحة المفاتيح');
}

// تبديل الشاشة الكاملة
function toggleFullscreen() {
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    document.documentElement.requestFullscreen();
  }
}

// تصدير وظائف للعملية الرئيسية
contextBridge.exposeInMainWorld('electronAPI', {
  toggleFullscreen: toggleFullscreen,
  removeAds: removeUnwantedElements
});

console.log('🚀 تم تحميل preload.js بنجاح');
