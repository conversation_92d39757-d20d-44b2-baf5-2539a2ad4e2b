import React, { useState } from 'react';
import DeveloperPanel from './DeveloperPanel';
import { adBlocker } from '../utils/adBlocker';
import { developerMonetization } from '../utils/developerMonetization';

const SettingsEnhanced = ({ settings, onSettingsChange, onClose, stats }) => {
  const [showDeveloperPanel, setShowDeveloperPanel] = useState(false);
  const [developerMode, setDeveloperMode] = useState(false);
  const [developerPassword, setDeveloperPassword] = useState('');

  const handleQualityChange = (e) => {
    onSettingsChange({ quality: e.target.value });
  };

  const handleToggle = (setting) => {
    onSettingsChange({ [setting]: !settings[setting] });
  };

  const handleDeveloperAccess = () => {
    // كلمة مرور بسيطة للوصول للوحة المطور
    const correctPassword = 'dev2024'; // يمكنك تغييرها
    
    if (developerPassword === correctPassword) {
      setDeveloperMode(true);
      setShowDeveloperPanel(true);
      setDeveloperPassword('');
    } else {
      alert('كلمة المرور غير صحيحة');
      setDeveloperPassword('');
    }
  };

  const qualityOptions = [
    { value: 'auto', label: 'تلقائي' },
    { value: 'hd2160', label: '4K (2160p)' },
    { value: 'hd1440', label: '1440p' },
    { value: 'hd1080', label: '1080p' },
    { value: 'hd720', label: '720p' },
    { value: 'large', label: '480p' },
    { value: 'medium', label: '360p' },
    { value: 'small', label: '240p' }
  ];

  const adStats = adBlocker.getStats();
  const monetizationStats = developerMonetization.getAnalytics();

  return (
    <>
      <div className="settings-overlay" onClick={onClose}>
        <div className="settings-modal" onClick={(e) => e.stopPropagation()}>
          <div className="settings-header">
            <h2 className="settings-title">إعدادات التطبيق</h2>
            <button className="close-btn" onClick={onClose}>✕</button>
          </div>

          <div className="settings-section">
            <h3 className="section-title">إعدادات الفيديو</h3>
            
            <div className="setting-item">
              <span className="setting-label">جودة الفيديو الافتراضية</span>
              <div className="setting-control">
                <select 
                  value={settings.quality} 
                  onChange={handleQualityChange}
                >
                  {qualityOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="setting-item">
              <span className="setting-label">إخفاء عناصر التحكم</span>
              <div 
                className={`toggle-switch ${settings.hideControls ? 'active' : ''}`}
                onClick={() => handleToggle('hideControls')}
              />
            </div>

            <div className="setting-item">
              <span className="setting-label">مستوى الصوت</span>
              <div className="setting-control">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.volume || 100}
                  onChange={(e) => onSettingsChange({ volume: parseInt(e.target.value) })}
                  className="volume-slider"
                />
                <span>{settings.volume || 100}%</span>
              </div>
            </div>
          </div>

          <div className="settings-section">
            <h3 className="section-title">إعدادات التطبيق</h3>
            
            <div className="setting-item">
              <span className="setting-label">مانع الإعلانات</span>
              <div 
                className={`toggle-switch ${settings.adBlock ? 'active' : ''}`}
                onClick={() => handleToggle('adBlock')}
              />
            </div>

            <div className="setting-item">
              <span className="setting-label">الثيم الداكن</span>
              <div 
                className={`toggle-switch ${settings.darkTheme ? 'active' : ''}`}
                onClick={() => handleToggle('darkTheme')}
              />
            </div>

            <div className="setting-item">
              <span className="setting-label">التشغيل التلقائي</span>
              <div 
                className={`toggle-switch ${settings.autoplay ? 'active' : ''}`}
                onClick={() => handleToggle('autoplay')}
              />
            </div>
          </div>

          <div className="settings-section">
            <h3 className="section-title">إحصائيات الاستخدام</h3>
            
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-value">{stats?.videosWatched || 0}</div>
                <div className="stat-label">فيديوهات مشاهدة</div>
              </div>

              <div className="stat-card">
                <div className="stat-value">{adStats.totalBlocked}</div>
                <div className="stat-label">إعلانات محجوبة</div>
              </div>

              <div className="stat-card">
                <div className="stat-value">{stats?.timeSpent || 0}د</div>
                <div className="stat-label">وقت الاستخدام</div>
              </div>

              <div className="stat-card">
                <div className="stat-value">{monetizationStats.impressions}</div>
                <div className="stat-label">مرات الظهور</div>
              </div>
            </div>
          </div>

          <div className="settings-section">
            <h3 className="section-title">معلومات التطبيق</h3>
            
            <div className="setting-item">
              <span className="setting-label">الإصدار</span>
              <span>1.0.0</span>
            </div>

            <div className="setting-item">
              <span className="setting-label">المطور</span>
              <span>YouTube Player Developer</span>
            </div>

            <div className="setting-item">
              <span className="setting-label">آخر تحديث</span>
              <span>{new Date().toLocaleDateString('ar-SA')}</span>
            </div>
          </div>

          {/* قسم المطور المخفي */}
          <div className="settings-section">
            <h3 className="section-title">إعدادات متقدمة</h3>
            
            {!developerMode ? (
              <div className="developer-access">
                <div className="setting-item">
                  <span className="setting-label">وضع المطور</span>
                  <div className="developer-login">
                    <input
                      type="password"
                      placeholder="كلمة مرور المطور"
                      value={developerPassword}
                      onChange={(e) => setDeveloperPassword(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleDeveloperAccess()}
                    />
                    <button onClick={handleDeveloperAccess}>دخول</button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="developer-controls">
                <button 
                  className="developer-panel-btn"
                  onClick={() => setShowDeveloperPanel(true)}
                >
                  💼 فتح لوحة تحكم المطور
                </button>
                
                <div className="developer-stats">
                  <p>🎯 معدل النقر: {monetizationStats.ctr}</p>
                  <p>💰 الأرباح: {monetizationStats.revenueFormatted}</p>
                  <p>📊 التحويلات: {monetizationStats.conversions}</p>
                </div>
              </div>
            )}
          </div>

          <div style={{ textAlign: 'center', marginTop: '20px', color: '#b3b3b3', fontSize: '12px' }}>
            <p>مشغل يوتيوب مخصص مع واجهة عربية</p>
            <p>جميع الحقوق محفوظة © 2024</p>
          </div>
        </div>
      </div>

      {showDeveloperPanel && (
        <DeveloperPanel onClose={() => setShowDeveloperPanel(false)} />
      )}
    </>
  );
};

export default SettingsEnhanced;

