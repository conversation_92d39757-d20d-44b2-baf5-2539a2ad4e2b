import React, { useEffect, useRef } from 'react';

const YouTubePlayer = ({ videoId, settings }) => {
  const playerRef = useRef(null);
  const playerInstanceRef = useRef(null);

  useEffect(() => {
    // تحميل YouTube IFrame API
    if (!window.YT) {
      const script = document.createElement('script');
      script.src = 'https://www.youtube.com/iframe_api';
      script.async = true;
      document.body.appendChild(script);

      window.onYouTubeIframeAPIReady = () => {
        initializePlayer();
      };
    } else {
      initializePlayer();
    }

    return () => {
      if (playerInstanceRef.current) {
        playerInstanceRef.current.destroy();
      }
    };
  }, []);

  useEffect(() => {
    if (playerInstanceRef.current && videoId) {
      playerInstanceRef.current.loadVideoById(videoId);
    }
  }, [videoId]);

  useEffect(() => {
    if (playerInstanceRef.current && settings) {
      // تطبيق إعدادات الجودة
      if (settings.quality !== 'auto') {
        playerInstanceRef.current.setPlaybackQuality(settings.quality);
      }
    }
  }, [settings]);

  const initializePlayer = () => {
    if (playerRef.current && window.YT && window.YT.Player) {
      playerInstanceRef.current = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: videoId || '',
        playerVars: {
          autoplay: 1,
          controls: settings?.hideControls ? 0 : 1,
          disablekb: settings?.hideControls ? 1 : 0,
          fs: 1,
          iv_load_policy: 3,
          modestbranding: 1,
          playsinline: 1,
          rel: 0,
          showinfo: 0,
          cc_load_policy: 0,
          hl: 'ar',
          cc_lang_pref: 'ar'
        },
        events: {
          onReady: (event) => {
            console.log('YouTube Player Ready');
            if (settings?.quality !== 'auto') {
              event.target.setPlaybackQuality(settings.quality);
            }
          },
          onStateChange: (event) => {
            console.log('Player State Changed:', event.data);
          },
          onError: (event) => {
            console.error('YouTube Player Error:', event.data);
          }
        }
      });
    }
  };

  if (!videoId) {
    return (
      <div className="youtube-player-container">
        <div className="player-placeholder">
          <h2>مرحباً بك في مشغل يوتيوب</h2>
          <p>ابحث عن فيديو أو الصق رابط يوتيوب لبدء المشاهدة</p>
          <div style={{ fontSize: '48px', margin: '20px 0' }}>📺</div>
          <p>استمتع بمشاهدة يوتيوب بدون إعلانات مع واجهة عربية مريحة</p>
        </div>
      </div>
    );
  }

  return (
    <div className="youtube-player-container">
      <div 
        ref={playerRef}
        className="youtube-player"
        id="youtube-player"
      />
    </div>
  );
};

export default YouTubePlayer;

