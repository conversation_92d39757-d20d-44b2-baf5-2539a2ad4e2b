import React, { useEffect, useRef, useState } from 'react';
import { YouTubeHelper } from '../utils/youtubeApi';

const YouTubePlayerEnhanced = ({ videoId, settings }) => {
  const playerRef = useRef(null);
  const playerInstanceRef = useRef(null);
  const [playerState, setPlayerState] = useState('unstarted');
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [videoInfo, setVideoInfo] = useState(null);
  const [availableQualities, setAvailableQualities] = useState([]);

  useEffect(() => {
    // تحميل YouTube IFrame API
    if (!window.YT) {
      const script = document.createElement('script');
      script.src = 'https://www.youtube.com/iframe_api';
      script.async = true;
      document.body.appendChild(script);

      window.onYouTubeIframeAPIReady = () => {
        initializePlayer();
      };
    } else {
      initializePlayer();
    }

    return () => {
      if (playerInstanceRef.current) {
        try {
          playerInstanceRef.current.destroy();
        } catch (error) {
          console.log('Error destroying player:', error);
        }
      }
    };
  }, []);

  useEffect(() => {
    if (playerInstanceRef.current && videoId) {
      loadVideo(videoId);
    }
  }, [videoId]);

  useEffect(() => {
    if (playerInstanceRef.current && settings) {
      applySettings();
    }
  }, [settings]);

  const initializePlayer = () => {
    if (playerRef.current && window.YT && window.YT.Player) {
      playerInstanceRef.current = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: videoId || '',
        playerVars: {
          autoplay: 0,
          controls: settings?.hideControls ? 0 : 1,
          disablekb: settings?.hideControls ? 1 : 0,
          fs: 1,
          iv_load_policy: 3,
          modestbranding: 1,
          playsinline: 1,
          rel: 0,
          showinfo: 0,
          cc_load_policy: 0,
          hl: 'ar',
          cc_lang_pref: 'ar',
          origin: window.location.origin
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
          onError: onPlayerError
        }
      });
    }
  };

  const onPlayerReady = (event) => {
    console.log('YouTube Player Ready');
    
    // الحصول على الجودات المتاحة
    const qualities = event.target.getAvailableQualityLevels();
    setAvailableQualities(qualities);
    
    // تطبيق الإعدادات الأولية
    applySettings();
    
    // بدء تحديث الوقت
    startTimeUpdater();
  };

  const onPlayerStateChange = (event) => {
    const states = {
      '-1': 'unstarted',
      '0': 'ended',
      '1': 'playing',
      '2': 'paused',
      '3': 'buffering',
      '5': 'cued'
    };
    
    const newState = states[event.data] || 'unknown';
    setPlayerState(newState);
    
    if (newState === 'playing') {
      setDuration(event.target.getDuration());
    }
  };

  const onPlayerError = (event) => {
    console.error('YouTube Player Error:', event.data);
    const errorMessages = {
      2: 'معرف الفيديو غير صحيح',
      5: 'خطأ في تشغيل الفيديو',
      100: 'الفيديو غير موجود أو محذوف',
      101: 'الفيديو غير متاح في منطقتك',
      150: 'الفيديو غير متاح في منطقتك'
    };
    
    const errorMessage = errorMessages[event.data] || 'خطأ غير معروف في تشغيل الفيديو';
    alert(errorMessage);
  };

  const loadVideo = async (videoId) => {
    if (playerInstanceRef.current) {
      try {
        playerInstanceRef.current.loadVideoById(videoId);
        
        // الحصول على معلومات الفيديو
        const info = await YouTubeHelper.getVideoInfo(videoId);
        setVideoInfo(info);
      } catch (error) {
        console.error('Error loading video:', error);
      }
    }
  };

  const applySettings = () => {
    if (!playerInstanceRef.current) return;

    try {
      // تطبيق إعدادات الجودة
      if (settings?.quality && settings.quality !== 'auto') {
        playerInstanceRef.current.setPlaybackQuality(settings.quality);
      }

      // تطبيق إعدادات الصوت
      if (settings?.volume !== undefined) {
        playerInstanceRef.current.setVolume(settings.volume);
        setVolume(settings.volume);
      }
    } catch (error) {
      console.error('Error applying settings:', error);
    }
  };

  const startTimeUpdater = () => {
    const updateTime = () => {
      if (playerInstanceRef.current && playerState === 'playing') {
        try {
          const time = playerInstanceRef.current.getCurrentTime();
          setCurrentTime(time);
        } catch (error) {
          console.error('Error getting current time:', error);
        }
      }
    };

    const interval = setInterval(updateTime, 1000);
    return () => clearInterval(interval);
  };

  // عناصر التحكم المخصصة
  const playPause = () => {
    if (!playerInstanceRef.current) return;
    
    if (playerState === 'playing') {
      playerInstanceRef.current.pauseVideo();
    } else {
      playerInstanceRef.current.playVideo();
    }
  };

  const seekTo = (time) => {
    if (playerInstanceRef.current) {
      playerInstanceRef.current.seekTo(time, true);
    }
  };

  const changeVolume = (newVolume) => {
    if (playerInstanceRef.current) {
      playerInstanceRef.current.setVolume(newVolume);
      setVolume(newVolume);
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      playerRef.current?.parentElement?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!videoId) {
    return (
      <div className="youtube-player-container">
        <div className="player-placeholder">
          <h2>مرحباً بك في مشغل يوتيوب</h2>
          <p>ابحث عن فيديو أو الصق رابط يوتيوب لبدء المشاهدة</p>
          <div style={{ fontSize: '48px', margin: '20px 0' }}>📺</div>
          <p>استمتع بمشاهدة يوتيوب بدون إعلانات مع واجهة عربية مريحة</p>
          
          <div style={{ marginTop: '30px', textAlign: 'center' }}>
            <h3>المميزات:</h3>
            <ul style={{ listStyle: 'none', padding: 0, marginTop: '15px' }}>
              <li>🚫 مانع إعلانات قوي</li>
              <li>🎨 واجهة عربية مع ثيم داكن</li>
              <li>⚙️ تحكم كامل في جودة الفيديو</li>
              <li>🔍 بحث سريع في يوتيوب</li>
              <li>📱 تصميم متجاوب</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="youtube-player-container">
      <div className="player-wrapper">
        <div 
          ref={playerRef}
          className="youtube-player"
          id="youtube-player"
        />
        
        {/* عناصر التحكم المخصصة */}
        {!settings?.hideControls && (
          <div className="custom-controls">
            <div className="controls-row">
              <button 
                className="control-btn"
                onClick={playPause}
                title={playerState === 'playing' ? 'إيقاف مؤقت' : 'تشغيل'}
              >
                {playerState === 'playing' ? '⏸️' : '▶️'}
              </button>
              
              <div className="time-display">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
              
              <div className="progress-container">
                <input
                  type="range"
                  min="0"
                  max={duration}
                  value={currentTime}
                  onChange={(e) => seekTo(parseFloat(e.target.value))}
                  className="progress-bar"
                />
              </div>
              
              <div className="volume-container">
                <span>🔊</span>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={volume}
                  onChange={(e) => changeVolume(parseInt(e.target.value))}
                  className="volume-bar"
                />
              </div>
              
              <button 
                className="control-btn"
                onClick={toggleFullscreen}
                title="ملء الشاشة"
              >
                {isFullscreen ? '🗗' : '🗖'}
              </button>
            </div>
          </div>
        )}
        
        {/* معلومات الفيديو */}
        {videoInfo && (
          <div className="video-info-overlay">
            <h3>{videoInfo.title}</h3>
            <p>{videoInfo.channel}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default YouTubePlayerEnhanced;

