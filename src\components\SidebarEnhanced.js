import React, { useState, useEffect } from 'react';
import { YouTubeHelper } from '../utils/youtubeApi';
import { channelConfig } from '../utils/channelConfig';

const SidebarEnhanced = ({ 
  searchQuery, 
  onVideoSelect, 
  currentVideoId,
  channelVideos = [],
  channelInfo = null,
  isLoadingChannel = false
}) => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('channel');

  // فيديوهات مقترحة افتراضية للبحث
  const defaultSearchVideos = [
    {
      id: 'dQw4w9WgXcQ',
      title: '<PERSON> - Never Gonna Give You Up (Official Video)',
      channel: '<PERSON>',
      duration: '3:33',
      views: '1.4B',
      publishedAt: '2009-10-25',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'
    },
    {
      id: 'kJQP7kiw5Fk',
      title: '<PERSON> Fonsi - Despacito ft. Daddy Yankee',
      channel: 'Luis Fonsi',
      duration: '4:42',
      views: '8.2B',
      publishedAt: '2017-01-12',
      thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'
    },
    {
      id: 'fJ9rUzIMcZQ',
      title: 'Queen – Bohemian Rhapsody (Official Video Remastered)',
      channel: 'Queen Official',
      duration: '5:55',
      views: '1.9B',
      publishedAt: '2008-08-01',
      thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'
    }
  ];

  useEffect(() => {
    if (searchQuery && searchQuery.trim()) {
      setActiveTab('search');
      performSearch(searchQuery);
    } else {
      setActiveTab('channel');
      setSearchResults([]);
    }
  }, [searchQuery]);

  const performSearch = async (query) => {
    setLoading(true);
    setError(null);
    
    try {
      const results = await YouTubeHelper.searchVideos(query, 20);
      setSearchResults(results.length > 0 ? results : defaultSearchVideos);
    } catch (error) {
      console.error('خطأ في البحث:', error);
      setError('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');
      setSearchResults(defaultSearchVideos);
    } finally {
      setLoading(false);
    }
  };

  const handleVideoClick = (videoId) => {
    onVideoSelect(videoId);
  };

  const formatViews = (views) => {
    if (typeof views === 'string') return views;
    
    if (views >= 1000000000) {
      return (views / 1000000000).toFixed(1) + 'B';
    } else if (views >= 1000000) {
      return (views / 1000000).toFixed(1) + 'M';
    } else if (views >= 1000) {
      return (views / 1000).toFixed(1) + 'K';
    }
    return views.toString();
  };

  const formatPublishedDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 7) {
      return `منذ ${diffDays} أيام`;
    } else if (diffDays < 30) {
      return `منذ ${Math.ceil(diffDays / 7)} أسابيع`;
    } else if (diffDays < 365) {
      return `منذ ${Math.ceil(diffDays / 30)} أشهر`;
    } else {
      return `منذ ${Math.ceil(diffDays / 365)} سنوات`;
    }
  };

  // عرض معلومات القناة
  const renderChannelHeader = () => {
    if (!channelInfo) return null;

    return (
      <div className="channel-header">
        <div className="channel-avatar">
          <div className="avatar-placeholder">
            {channelInfo.name.charAt(0)}
          </div>
        </div>
        <div className="channel-info">
          <h3 className="channel-name">{channelInfo.name}</h3>
          <p className="channel-subscribers">{channelInfo.subscribers} مشترك</p>
          <p className="channel-description">{channelInfo.description}</p>
        </div>
        <button 
          className="subscribe-btn"
          onClick={() => window.open(channelConfig.developer.socialLinks.youtube, '_blank')}
        >
          اشتراك
        </button>
      </div>
    );
  };

  // عرض قائمة الفيديوهات
  const renderVideoList = (videoList, isChannelVideos = false) => {
    if (videoList.length === 0) {
      return (
        <div className="no-videos">
          <p>لا توجد فيديوهات متاحة</p>
        </div>
      );
    }

    return (
      <div className="video-list">
        {videoList.map((video) => (
          <div
            key={video.id}
            className={`video-item ${currentVideoId === video.id ? 'active' : ''}`}
            onClick={() => handleVideoClick(video.id)}
          >
            <div style={{ position: 'relative' }}>
              <img 
                src={video.thumbnail || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+'}
                alt={video.title}
                className="video-thumbnail"
                onError={(e) => {
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+';
                }}
              />
              {video.duration && (
                <div className="video-duration">
                  {video.duration}
                </div>
              )}
            </div>
            
            <div className="video-info">
              <div className="video-title" title={video.title}>
                {video.title}
              </div>
              <div className="video-channel">
                {isChannelVideos ? channelInfo?.name : (video.channel || video.channelName)}
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '5px' }}>
                {video.views && (
                  <div className="video-views">
                    👁️ {formatViews(video.views)} مشاهدة
                  </div>
                )}
                {(video.publishedAt || video.uploadDate) && (
                  <div className="video-published">
                    {formatPublishedDate(video.publishedAt || video.uploadDate)}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="sidebar">
      {/* تبويبات */}
      <div className="sidebar-tabs">
        <button 
          className={`tab-btn ${activeTab === 'channel' ? 'active' : ''}`}
          onClick={() => setActiveTab('channel')}
        >
          🏠 {channelConfig.developer.channelName}
        </button>
        {searchQuery && (
          <button 
            className={`tab-btn ${activeTab === 'search' ? 'active' : ''}`}
            onClick={() => setActiveTab('search')}
          >
            🔍 البحث
          </button>
        )}
      </div>

      {/* محتوى الشريط الجانبي */}
      <div className="sidebar-content">
        {activeTab === 'channel' && (
          <div className="channel-tab">
            {renderChannelHeader()}
            
            {isLoadingChannel ? (
              <div style={{ textAlign: 'center', padding: '30px', color: '#b3b3b3' }}>
                <div className="loading-spinner"></div>
                <p style={{ marginTop: '10px' }}>جاري تحميل فيديوهات القناة...</p>
              </div>
            ) : (
              <>
                <div className="section-title">
                  <h4>أحدث الفيديوهات</h4>
                </div>
                {renderVideoList(channelVideos, true)}
              </>
            )}
          </div>
        )}

        {activeTab === 'search' && (
          <div className="search-tab">
            <div className="sidebar-header">
              <h3 className="sidebar-title">
                <span>🔍</span>
                <span>نتائج البحث: {searchQuery}</span>
              </h3>
              
              {searchResults.length > 0 && !loading && (
                <div style={{ fontSize: '12px', color: 'var(--secondary-text)', marginTop: '5px' }}>
                  {searchResults.length} نتيجة
                </div>
              )}
            </div>
            
            {loading ? (
              <div style={{ textAlign: 'center', padding: '30px', color: '#b3b3b3' }}>
                <div className="loading-spinner"></div>
                <p style={{ marginTop: '10px' }}>جاري البحث...</p>
              </div>
            ) : error ? (
              <div className="error-message">
                <p>{error}</p>
                <button 
                  onClick={() => performSearch(searchQuery)}
                  style={{ 
                    background: 'none', 
                    border: '1px solid white', 
                    color: 'white', 
                    padding: '5px 10px', 
                    borderRadius: '3px', 
                    marginTop: '10px',
                    cursor: 'pointer'
                  }}
                >
                  إعادة المحاولة
                </button>
              </div>
            ) : (
              renderVideoList(searchResults, false)
            )}
          </div>
        )}
      </div>

      {/* معلومات المطور */}
      <div className="sidebar-footer">
        <div className="developer-credit">
          <p>تم التطوير بواسطة:</p>
          <p><strong>{channelConfig.developer.name}</strong></p>
          <p>{channelConfig.developer.phone}</p>
          <p>{channelConfig.developer.channelName}</p>
        </div>
      </div>
    </div>
  );
};

export default SidebarEnhanced;

