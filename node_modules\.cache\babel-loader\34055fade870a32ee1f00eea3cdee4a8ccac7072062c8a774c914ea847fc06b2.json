{"ast": null, "code": "import React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Header=_ref=>{let{onVideoLoad,onSettingsClick}=_ref;const[searchInput,setSearchInput]=useState('');const handleSearch=e=>{e.preventDefault();if(searchInput.trim()){onVideoLoad(searchInput.trim());setSearchInput('');}};const handleMinimize=()=>{if(window.require){const{ipcRenderer}=window.require('electron');ipcRenderer.invoke('minimize-window');}};const handleMaximize=()=>{if(window.require){const{ipcRenderer}=window.require('electron');ipcRenderer.invoke('maximize-window');}};const handleClose=()=>{if(window.require){const{ipc<PERSON>enderer}=window.require('electron');ipcRenderer.invoke('close-window');}};return/*#__PURE__*/_jsxs(\"header\",{className:\"header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"header-right\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"logo\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCFA\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0645\\u0634\\u063A\\u0644 \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSearch,className:\"search-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"search-input\",placeholder:\"\\u0627\\u0628\\u062D\\u062B \\u0641\\u064A \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628 \\u0623\\u0648 \\u0627\\u0644\\u0635\\u0642 \\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648...\",value:searchInput,onChange:e=>setSearchInput(e.target.value)}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"search-btn\",children:\"\\uD83D\\uDD0D\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-left\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"header-btn\",onClick:onSettingsClick,title:\"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\",children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsx(\"button\",{className:\"header-btn\",onClick:handleMinimize,title:\"\\u062A\\u0635\\u063A\\u064A\\u0631\",children:\"\\u2796\"}),/*#__PURE__*/_jsx(\"button\",{className:\"header-btn\",onClick:handleMaximize,title:\"\\u062A\\u0643\\u0628\\u064A\\u0631/\\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629\",children:\"\\u2B1C\"}),/*#__PURE__*/_jsx(\"button\",{className:\"header-btn\",onClick:handleClose,title:\"\\u0625\\u063A\\u0644\\u0627\\u0642\",children:\"\\u274C\"})]})]});};export default Header;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "_ref", "onVideoLoad", "onSettingsClick", "searchInput", "setSearchInput", "handleSearch", "e", "preventDefault", "trim", "handleMinimize", "window", "require", "ip<PERSON><PERSON><PERSON><PERSON>", "invoke", "handleMaximize", "handleClose", "className", "children", "onSubmit", "type", "placeholder", "value", "onChange", "target", "onClick", "title"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/components/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Header = ({ onVideoLoad, onSettingsClick }) => {\n  const [searchInput, setSearchInput] = useState('');\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchInput.trim()) {\n      onVideoLoad(searchInput.trim());\n      setSearchInput('');\n    }\n  };\n\n  const handleMinimize = () => {\n    if (window.require) {\n      const { ipcRenderer } = window.require('electron');\n      ipcRenderer.invoke('minimize-window');\n    }\n  };\n\n  const handleMaximize = () => {\n    if (window.require) {\n      const { ipcRenderer } = window.require('electron');\n      ipcRenderer.invoke('maximize-window');\n    }\n  };\n\n  const handleClose = () => {\n    if (window.require) {\n      const { ipcRenderer } = window.require('electron');\n      ipcRenderer.invoke('close-window');\n    }\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"header-right\">\n        <div className=\"logo\">\n          <span>📺</span>\n          <span>مشغل يوتيوب</span>\n        </div>\n        \n        <form onSubmit={handleSearch} className=\"search-container\">\n          <input\n            type=\"text\"\n            className=\"search-input\"\n            placeholder=\"ابحث في يوتيوب أو الصق رابط الفيديو...\"\n            value={searchInput}\n            onChange={(e) => setSearchInput(e.target.value)}\n          />\n          <button type=\"submit\" className=\"search-btn\">\n            🔍\n          </button>\n        </form>\n      </div>\n\n      <div className=\"header-left\">\n        <button \n          className=\"header-btn\"\n          onClick={onSettingsClick}\n          title=\"الإعدادات\"\n        >\n          ⚙️\n        </button>\n        \n        <button \n          className=\"header-btn\"\n          onClick={handleMinimize}\n          title=\"تصغير\"\n        >\n          ➖\n        </button>\n        \n        <button \n          className=\"header-btn\"\n          onClick={handleMaximize}\n          title=\"تكبير/استعادة\"\n        >\n          ⬜\n        </button>\n        \n        <button \n          className=\"header-btn\"\n          onClick={handleClose}\n          title=\"إغلاق\"\n        >\n          ❌\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAAsC,IAArC,CAAEC,WAAW,CAAEC,eAAgB,CAAC,CAAAF,IAAA,CAC9C,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGV,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAAW,YAAY,CAAIC,CAAC,EAAK,CAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAIJ,WAAW,CAACK,IAAI,CAAC,CAAC,CAAE,CACtBP,WAAW,CAACE,WAAW,CAACK,IAAI,CAAC,CAAC,CAAC,CAC/BJ,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAK,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIC,MAAM,CAACC,OAAO,CAAE,CAClB,KAAM,CAAEC,WAAY,CAAC,CAAGF,MAAM,CAACC,OAAO,CAAC,UAAU,CAAC,CAClDC,WAAW,CAACC,MAAM,CAAC,iBAAiB,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIJ,MAAM,CAACC,OAAO,CAAE,CAClB,KAAM,CAAEC,WAAY,CAAC,CAAGF,MAAM,CAACC,OAAO,CAAC,UAAU,CAAC,CAClDC,WAAW,CAACC,MAAM,CAAC,iBAAiB,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAIL,MAAM,CAACC,OAAO,CAAE,CAClB,KAAM,CAAEC,WAAY,CAAC,CAAGF,MAAM,CAACC,OAAO,CAAC,UAAU,CAAC,CAClDC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAC,CACpC,CACF,CAAC,CAED,mBACEf,KAAA,WAAQkB,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACxBnB,KAAA,QAAKkB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnB,KAAA,QAAKkB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrB,IAAA,SAAAqB,QAAA,CAAM,cAAE,CAAM,CAAC,cACfrB,IAAA,SAAAqB,QAAA,CAAM,+DAAW,CAAM,CAAC,EACrB,CAAC,cAENnB,KAAA,SAAMoB,QAAQ,CAAEb,YAAa,CAACW,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACxDrB,IAAA,UACEuB,IAAI,CAAC,MAAM,CACXH,SAAS,CAAC,cAAc,CACxBI,WAAW,CAAC,yLAAwC,CACpDC,KAAK,CAAElB,WAAY,CACnBmB,QAAQ,CAAGhB,CAAC,EAAKF,cAAc,CAACE,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE,CACjD,CAAC,cACFzB,IAAA,WAAQuB,IAAI,CAAC,QAAQ,CAACH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAE7C,CAAQ,CAAC,EACL,CAAC,EACJ,CAAC,cAENnB,KAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrB,IAAA,WACEoB,SAAS,CAAC,YAAY,CACtBQ,OAAO,CAAEtB,eAAgB,CACzBuB,KAAK,CAAC,wDAAW,CAAAR,QAAA,CAClB,cAED,CAAQ,CAAC,cAETrB,IAAA,WACEoB,SAAS,CAAC,YAAY,CACtBQ,OAAO,CAAEf,cAAe,CACxBgB,KAAK,CAAC,gCAAO,CAAAR,QAAA,CACd,QAED,CAAQ,CAAC,cAETrB,IAAA,WACEoB,SAAS,CAAC,YAAY,CACtBQ,OAAO,CAAEV,cAAe,CACxBW,KAAK,CAAC,2EAAe,CAAAR,QAAA,CACtB,QAED,CAAQ,CAAC,cAETrB,IAAA,WACEoB,SAAS,CAAC,YAAY,CACtBQ,OAAO,CAAET,WAAY,CACrBU,KAAK,CAAC,gCAAO,CAAAR,QAAA,CACd,QAED,CAAQ,CAAC,EACN,CAAC,EACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAlB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}