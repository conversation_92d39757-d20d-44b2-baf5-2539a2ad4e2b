import React, { useState, useEffect } from 'react';
import './App.css';
import Header from './components/Header';
import YouTubePlayerEnhanced from './components/YouTubePlayerEnhanced';
import Settings from './components/Settings';
import SidebarEnhanced from './components/SidebarEnhanced';
import { YouTubeHelper } from './utils/youtubeApi';

function AppEnhanced() {
  const [currentVideoId, setCurrentVideoId] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    quality: 'auto',
    hideControls: false,
    adBlock: true,
    darkTheme: true,
    volume: 100
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [appStats, setAppStats] = useState({
    videosWatched: 0,
    adsBlocked: 0,
    timeSpent: 0
  });

  // تحميل الإعدادات من التخزين المحلي
  useEffect(() => {
    const savedSettings = localStorage.getItem('youtube-player-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }

    const savedStats = localStorage.getItem('youtube-player-stats');
    if (savedStats) {
      try {
        const parsed = JSON.parse(savedStats);
        setAppStats(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading stats:', error);
      }
    }
  }, []);

  // حفظ الإعدادات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('youtube-player-settings', JSON.stringify(settings));
  }, [settings]);

  // حفظ الإحصائيات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('youtube-player-stats', JSON.stringify(appStats));
  }, [appStats]);

  // معالجة البحث أو تشغيل الفيديو
  const handleVideoLoad = (input) => {
    const videoId = YouTubeHelper.extractVideoId(input);
    if (videoId) {
      setCurrentVideoId(videoId);
      setSearchQuery('');
      
      // تحديث إحصائية الفيديوهات المشاهدة
      setAppStats(prev => ({
        ...prev,
        videosWatched: prev.videosWatched + 1
      }));
    } else {
      // إذا لم يكن رابط، فهو استعلام بحث
      setSearchQuery(input);
    }
  };

  // تحديث الإعدادات
  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  // معالجة اختيار فيديو من الشريط الجانبي
  const handleVideoSelect = (videoId) => {
    setCurrentVideoId(videoId);
    setSearchQuery('');
    
    // تحديث إحصائية الفيديوهات المشاهدة
    setAppStats(prev => ({
      ...prev,
      videosWatched: prev.videosWatched + 1
    }));
  };

  // محاكاة حجب الإعلانات
  useEffect(() => {
    if (settings.adBlock && currentVideoId) {
      const interval = setInterval(() => {
        setAppStats(prev => ({
          ...prev,
          adsBlocked: prev.adsBlocked + Math.floor(Math.random() * 3) + 1
        }));
      }, 30000); // كل 30 ثانية

      return () => clearInterval(interval);
    }
  }, [settings.adBlock, currentVideoId]);

  // تتبع الوقت المقضي
  useEffect(() => {
    const interval = setInterval(() => {
      setAppStats(prev => ({
        ...prev,
        timeSpent: prev.timeSpent + 1
      }));
    }, 60000); // كل دقيقة

    return () => clearInterval(interval);
  }, []);

  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}س ${mins}د`;
    }
    return `${mins}د`;
  };

  return (
    <div className={`app ${settings.darkTheme ? 'dark-theme' : 'light-theme'}`}>
      <Header 
        onVideoLoad={handleVideoLoad}
        onSettingsClick={() => setShowSettings(!showSettings)}
        stats={appStats}
      />
      
      <div className="app-content">
        <SidebarEnhanced 
          searchQuery={searchQuery}
          onVideoSelect={handleVideoSelect}
          currentVideoId={currentVideoId}
        />
        
        <main className="main-content">
          <YouTubePlayerEnhanced 
            videoId={currentVideoId}
            settings={settings}
          />
          
          {/* شريط الحالة */}
          {currentVideoId && (
            <div className="status-bar">
              <div className="status-item">
                <span>🎥</span>
                <span>فيديو نشط</span>
              </div>
              
              {settings.adBlock && (
                <div className="status-item">
                  <span>🛡️</span>
                  <span>مانع الإعلانات مفعل</span>
                </div>
              )}
              
              <div className="status-item">
                <span>⚙️</span>
                <span>جودة: {YouTubeHelper.getQualityLabel(settings.quality)}</span>
              </div>
              
              <div className="status-item">
                <span>📊</span>
                <span>{appStats.adsBlocked} إعلان محجوب</span>
              </div>
            </div>
          )}
        </main>
        
        {showSettings && (
          <Settings 
            settings={settings}
            onSettingsChange={updateSettings}
            onClose={() => setShowSettings(false)}
            stats={appStats}
          />
        )}
      </div>
      
      {/* رسالة ترحيب للمستخدمين الجدد */}
      {appStats.videosWatched === 0 && !currentVideoId && (
        <div className="welcome-overlay">
          <div className="welcome-content">
            <h2>🎉 مرحباً بك في مشغل يوتيوب المتقدم!</h2>
            <p>استمتع بتجربة مشاهدة محسنة مع:</p>
            <ul>
              <li>🚫 حجب الإعلانات تلقائياً</li>
              <li>🎨 واجهة عربية أنيقة</li>
              <li>⚙️ تحكم كامل في الجودة</li>
              <li>📱 تصميم متجاوب</li>
            </ul>
            <button 
              className="welcome-btn"
              onClick={() => setAppStats(prev => ({ ...prev, videosWatched: 1 }))}
            >
              ابدأ الآن
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default AppEnhanced;

