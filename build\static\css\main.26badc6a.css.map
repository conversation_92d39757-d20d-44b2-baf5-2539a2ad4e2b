{"version": 3, "file": "static/css/main.26badc6a.css", "mappings": "AACA,MACE,oBAAqB,CACrB,sBAAuB,CACvB,mBAAoB,CACpB,mBAAuB,CACvB,wBAAyB,CACzB,kBAAuB,CACvB,sBAAuB,CACvB,kBAAmB,CACnB,uBAAwB,CACxB,uBAAwB,CACxB,qBACF,CAEA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAGE,UAA0B,CAA1B,yBAA0B,CAC1B,aAAc,CAHd,4CAAsD,CAItD,eACF,CAEA,UANE,wBAAmC,CAAnC,kCAWF,CALA,KAEE,YAAa,CACb,qBAAsB,CAFtB,YAIF,CAEA,aACE,YAAa,CACb,QAAO,CACP,eACF,CAEA,cAIE,wBAAmC,CAAnC,kCAAmC,CAFnC,YAAa,CADb,QAAO,CAEP,qBAEF,CAGA,QACE,wBAAqC,CAArC,oCAAqC,CAKrC,+BAA4C,CAA5C,2CAA4C,CAD5C,6BAA8B,CAE9B,eAAgB,CALhB,iBAMF,CAEA,sBANE,kBAAmB,CADnB,YAWF,CAJA,cAGE,QACF,CAEA,MAME,SAA0B,CAA1B,yBAA0B,CAF1B,cAAe,CACf,eAAiB,CAFjB,QAIF,CAEA,wBAPE,kBAAmB,CADnB,YAeF,CAPA,kBAGE,wBAAkC,CAAlC,iCAAkC,CAClC,kBAAmB,CAEnB,eAAgB,CADhB,gBAEF,CAEA,cACE,eAAgB,CAChB,WAAY,CACZ,UAA0B,CAA1B,yBAA0B,CAI1B,aAAc,CAHd,cAAe,CAEf,YAAa,CADb,UAGF,CAMA,uCAHE,aAA4B,CAA5B,2BAUF,CAPA,YACE,eAAgB,CAChB,WAAY,CAEZ,cAAe,CAEf,gBAAiB,CADjB,WAEF,CAEA,kBACE,UAA0B,CAA1B,yBACF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,YACE,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAHlB,aAA4B,CAA5B,2BAA4B,CAC5B,cAAe,CACf,WAAY,CAEZ,uBACF,CAEA,kBACE,wBAAiC,CAAjC,gCAAiC,CACjC,UAA0B,CAA1B,yBACF,CAGA,SAEE,wBAAqC,CAArC,oCAAqC,CACrC,6BAA0C,CAA1C,yCAA0C,CAC1C,YAAa,CACb,qBAAsB,CACtB,eAAgB,CALhB,WAMF,CAEA,gBAEE,+BAA4C,CAA5C,2CAA4C,CAD5C,YAEF,CAEA,eACE,cAAe,CACf,eAAiB,CACjB,kBACF,CAEA,iBACE,QAAO,CACP,eAAgB,CAChB,YACF,CAEA,YAIE,iBAAkB,CAClB,cAAe,CAJf,YAAa,CACb,QAAS,CAKT,kBAAmB,CAJnB,YAAa,CAGb,oCAEF,CAEA,kBACE,wBAAiC,CAAjC,gCACF,CAEA,iBAGE,iBAAkB,CADlB,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,YACE,QACF,CAEA,aAME,oBAAqB,CACrB,2BAA4B,CAF5B,mBAAoB,CAJpB,cAAe,CACf,eAAgB,CAEhB,eAAgB,CADhB,iBAAkB,CAKlB,eACF,CAEA,eAEE,aAA4B,CAA5B,2BAA4B,CAD5B,cAEF,CAGA,0BAGE,kBAAmB,CAEnB,qBAAsB,CAHtB,YAAa,CADb,QAAO,CAGP,sBAAuB,CAEvB,iBACF,CAEA,gBAEE,WAAY,CADZ,UAEF,CAEA,oBAEE,aAA4B,CAA5B,2BAA4B,CAD5B,iBAEF,CAEA,uBACE,cAAe,CACf,kBACF,CAEA,sBACE,cACF,CAGA,kBAQE,kBAAmB,CAFnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,gBACE,wBAAqC,CAArC,oCAAqC,CACrC,kBAAmB,CAInB,eAAgB,CADhB,cAAe,CAEf,eAAgB,CAJhB,YAAa,CACb,WAIF,CAEA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,gBACE,cAAe,CACf,eACF,CAEA,WACE,eAAgB,CAChB,WAAY,CACZ,aAA4B,CAA5B,2BAA4B,CAC5B,cAAe,CACf,cAAe,CACf,WACF,CAEA,iBACE,UAA0B,CAA1B,yBACF,CAEA,kBACE,kBACF,CAEA,eAIE,SAA0B,CAA1B,yBAA0B,CAH1B,cAAe,CACf,eAAgB,CAChB,kBAEF,CAEA,cAGE,kBAAmB,CAGnB,wBAAkC,CAAlC,iCAAkC,CAClC,iBAAkB,CANlB,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,YAGF,CAEA,eACE,cACF,CAEA,wBACE,wBAAmC,CAAnC,kCAAmC,CAEnC,wBAAqC,CAArC,oCAAqC,CACrC,iBAAkB,CAFlB,UAA0B,CAA1B,yBAA0B,CAI1B,cAAe,CADf,gBAEF,CAEA,eAIE,wBAAqC,CAArC,oCAAqC,CACrC,kBAAmB,CACnB,cAAe,CAHf,WAAY,CAFZ,iBAAkB,CAMlB,oCAAsC,CALtC,UAMF,CAEA,sBACE,oBAAqC,CAArC,oCACF,CAEA,qBAOE,qBAAuB,CACvB,iBAAkB,CAPlB,UAAW,CAKX,WAAY,CAJZ,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAMR,6BAA+B,CAJ/B,UAKF,CAEA,4BACE,2BACF,CAGA,yBACE,SACE,WACF,CAEA,kBACE,eACF,CAEA,gBAEE,YAAa,CADb,UAEF,CACF,CAGA,oBACE,SACF,CAEA,0BACE,kBAA+B,CAA/B,8BACF,CAEA,0BACE,kBAA+B,CAA/B,8BAA+B,CAC/B,iBACF,CAEA,gCACE,kBAA2B,CAA3B,0BACF,CAKA,gBAGE,WAAY,CAFZ,iBAAkB,CAClB,UAEF,CAEA,iBAKE,uCAA4D,CAH5D,QAAS,CACT,MAAO,CAIP,SAAU,CADV,YAAa,CALb,iBAAkB,CAGlB,OAAQ,CAIR,2BACF,CAEA,uCACE,SACF,CAEA,cAEE,kBAAmB,CAEnB,UAAY,CAHZ,YAAa,CAEb,QAEF,CAEA,aACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,UAAY,CACZ,cAAe,CACf,cAAe,CACf,WAAY,CAEZ,oCACF,CAEA,mBACE,sBACF,CAEA,cACE,cAAe,CACf,eAAgB,CAChB,eAAgB,CAChB,iBACF,CAEA,oBACE,QAAO,CACP,aACF,CAEA,cAGE,oBAAoC,CACpC,iBAAkB,CAElB,cAAe,CAJf,UAAW,CAGX,YAAa,CAJb,UAMF,CAEA,oCACE,eAAgB,CAGhB,cAA+B,CAA/B,8BAA+B,CAC/B,iBAAkB,CAClB,cAAe,CAHf,WAAY,CADZ,UAKF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,YAGE,oBAAoC,CACpC,iBAAkB,CAElB,cAAe,CAJf,UAAW,CAGX,YAAa,CAJb,UAMF,CAEA,kCACE,eAAgB,CAGhB,eAAiB,CACjB,iBAAkB,CAClB,cAAe,CAHf,WAAY,CADZ,UAKF,CAEA,oBAIE,oBAA8B,CAE9B,iBAAkB,CAClB,UAAY,CAJZ,SAAU,CAKV,eAAgB,CAChB,SAAU,CAJV,iBAAkB,CAJlB,iBAAkB,CAClB,QAAS,CAQT,2BACF,CAEA,0CACE,SACF,CAEA,uBACE,cAAe,CAEf,eAAgB,CADhB,iBAEF,CAEA,sBAEE,UAAW,CADX,cAAe,CAEf,QACF,CAGA,YACE,iBACF,CAEA,gBAIE,gBAA8B,CAG9B,iBAAkB,CALlB,UAAW,CAGX,UAAY,CAGZ,cAAe,CACf,eAAgB,CAHhB,eAAgB,CALhB,iBAAkB,CAElB,SAOF,CAEA,aAGE,cACF,CAEA,8BAJE,aAA4B,CAA5B,2BAA4B,CAD5B,cAQF,CAGA,kBACE,+BAA4C,CAA5C,2CAA4C,CAC5C,mBACF,CAEA,6BACE,kBAAmB,CACnB,gBACF,CAEA,iBAEE,kBAAmB,CAInB,wBAAmC,CAAnC,kCAAmC,CACnC,iBAAkB,CANlB,YAAa,CAEb,QAAS,CACT,eAAgB,CAChB,YAGF,CAEA,mBAIE,wBAAsC,CAAtC,qCAAsC,CADtC,iBAAkB,CADlB,WAAY,CADZ,UAIF,CAEA,qBAEE,aAA4B,CAA5B,2BAA4B,CAD5B,cAEF,CAGA,iBAOE,sCAAuC,CADvC,wBAAqC,CADrC,iBAAkB,CAClB,oBAAqC,CAArC,oCAAqC,CAArC,oCAAqC,CALrC,oBAAqB,CAErB,WAAY,CADZ,UAMF,CAEA,gBACE,GAAK,uBAA2B,CAClC,CAEA,eACE,wBAAoC,CAApC,mCAMF,CAEA,gCALE,iBAAkB,CAFlB,UAAY,CAGZ,aAAc,CAFd,YAAa,CAGb,iBAUF,CAPA,iBACE,wBAAsC,CAAtC,qCAMF,CAGA,yBACE,cACE,OACF,CAEA,cACE,cAAe,CACf,cACF,CAEA,kBACE,YACF,CAEA,oBAEE,cAAe,CADf,eAEF,CACF,CAGA,SACE,4BACF,CAEA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAEA,UACE,8BACF,CASA,YAKE,wBAAqC,CAArC,oCAAqC,CACrC,4BAAyC,CAAzC,wCAAyC,CAEzC,aAA4B,CAA5B,2BAA4B,CAD5B,cAAe,CAJf,QAAS,CACT,gBAKF,CAEA,yBATE,kBAAmB,CADnB,YAcF,CAJA,aAGE,OACF,CAGA,iBAQE,kBAAmB,CAGnB,4BAA8B,CAL9B,sBAAoC,CADpC,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,iBACE,wBAAqC,CAArC,oCAAqC,CAKrC,oBAAqC,CAArC,oCAAqC,CAJrC,kBAAmB,CAGnB,eAAgB,CAFhB,YAAa,CACb,iBAGF,CAEA,oBACE,SAA0B,CAA1B,yBAA0B,CAE1B,cAAe,CADf,kBAEF,CAEA,mBAEE,UAA0B,CAA1B,yBAA0B,CAD1B,kBAEF,CAEA,oBAEE,aAAc,CACd,kBAAmB,CAFnB,gBAGF,CAEA,oBAEE,UAA0B,CAA1B,yBAA0B,CAD1B,kBAEF,CAEA,aACE,oBAAqC,CAArC,oCAAqC,CAErC,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAiB,CAHjB,iBAAkB,CAKlB,uBACF,CAEA,mBACE,qBAAyB,CACzB,0BACF,CAGA,mBACE,oBAAqC,CAArC,oCAAqC,CACrC,UACF,CAEA,sGAGE,WACF,CAGA,iBACE,iBAAqB,CACrB,sBAAuB,CACvB,mBAAoB,CACpB,mBAAuB,CACvB,qBAAyB,CACzB,mBAAuB,CACvB,kBACF,CAGA,iBACE,6BACF,CAEA,mCACE,qBACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QAAS,CACT,YACF,CAEA,cACE,aAA4B,CAA5B,2BAA4B,CAC5B,cACF,CAGA,mBAGE,4BAAyC,CAAzC,wCAAyC,CAFzC,eAAgB,CAChB,gBAEF,CAEA,YAIE,eACF,CAEA,WACE,wBAAkC,CAAlC,iCAAkC,CAElC,iBAAkB,CADlB,YAAa,CAEb,iBACF,CAEA,YAGE,SAA0B,CAA1B,yBAA0B,CAF1B,cAAe,CACf,eAAiB,CAEjB,iBACF,CAEA,YAEE,aAA4B,CAA5B,2BAA4B,CAD5B,cAEF,CAGA,gBACE,mCACF,CAEA,wBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,WACE,+BACF,CAEA,oBACE,GAEE,SAAU,CADV,mBAEF,CACA,IACE,qBACF,CACA,IACE,mBACF,CACA,GAEE,SAAU,CADV,kBAEF,CACF,CAGA,SAOE,kBAAsB,CAEtB,QAAS,CANT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAOlB,kBAAmB,CANnB,SAQF,CAGA,aACE,+CAIE,YACF,CAEA,cACE,UACF,CACF,CAIA,mBAME,sBAAoC,CAIpC,YACF,CAEA,iBACE,wBAAqC,CAArC,oCAAqC,CAOrC,oBAAqC,CAArC,oCAAqC,CANrC,kBAAmB,CAInB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAJhB,SAAU,CACV,UAKF,CAEA,cAME,kBAAmB,CALnB,oBAAqC,CAArC,oCAAqC,CACrC,UAAY,CAEZ,YAAa,CACb,6BAA8B,CAF9B,YAIF,CAEA,iBAEE,cAAe,CADf,QAEF,CAEA,YAEE,wBAAkC,CAAlC,iCAAkC,CAClC,+BAA4C,CAA5C,2CAA4C,CAF5C,YAGF,CAEA,SAQE,cAAe,CAHf,iBAIF,CAaA,aAEE,eAAgB,CAChB,eAAgB,CAFhB,YAGF,CAGA,kBACE,kBACF,CAEA,cACE,2CAAiE,CAAjE,2DAAiE,CAGjE,kBAAmB,CAFnB,UAAY,CAIZ,kBAAmB,CAHnB,YAAa,CAEb,iBAEF,CAEA,mBACE,cACF,CAEA,cACE,cAAe,CACf,kBACF,CAEA,eACE,cAAe,CACf,eAAiB,CACjB,iBACF,CAEA,eACE,cAAe,CACf,UACF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAGA,uCAEE,wBAAkC,CAAlC,iCAAkC,CAElC,kBAAmB,CACnB,kBAAmB,CAFnB,YAGF,CAEA,YACE,kBACF,CAEA,kBAIE,UAA0B,CAA1B,yBAA0B,CAH1B,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,0DAOE,wBAAmC,CAAnC,kCAAmC,CAFnC,wBAAqC,CAArC,oCAAqC,CACrC,iBAAkB,CAElB,UAA0B,CAA1B,yBAA0B,CAC1B,cAAe,CALf,YAAa,CADb,UAOF,CAEA,qBAEE,eAAgB,CADhB,eAEF,CAEA,SACE,oBAAqC,CAArC,oCAAqC,CAErC,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,iBAAkB,CAKlB,uBACF,CAEA,eACE,qBAAyB,CACzB,0BACF,CAGA,YACE,wBAAkC,CAAlC,iCAAkC,CAElC,kBAAmB,CACnB,eAAgB,CAFhB,YAGF,CAEA,YAIE,wBAAmC,CAAnC,kCAAmC,CAHnC,yBAAsC,CAAtC,qCAIF,CAEA,wBAJE,iBAAkB,CADlB,YAYF,CAPA,YAGE,wBAAqC,CAArC,oCAAqC,CAErC,cAAe,CAJf,iBAAkB,CAKlB,uBACF,CAEA,kBAEE,+BAAyC,CADzC,0BAEF,CAEA,YAEE,UAA0B,CAA1B,yBAA0B,CAC1B,cAAe,CAFf,cAGF,CAEA,WAEE,aAA4B,CAA5B,2BAA4B,CAC5B,cAAe,CACf,eAAgB,CAHhB,QAIF,CAEA,UAIE,oBAAqC,CAArC,oCAAqC,CAGrC,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,eAAiB,CANjB,QAAS,CAGT,eAAgB,CALhB,iBAAkB,CAClB,OAQF,CAGA,sBAIE,8BAAgC,CAFhC,iBAAkB,CADlB,aAAc,CAEd,eAEF,CAEA,uBAEE,iBAAkB,CAElB,cAAe,CAHf,aAAc,CAEd,eAEF,CAEA,mCACE,YACF,CAEA,mCACE,cAAe,CACf,iBACF,CAEA,kCACE,cACF,CAGA,gBAGE,4BAA6C,CAA7C,4CAA6C,CAF7C,SAA0B,CAA1B,yBAA0B,CAC1B,oBAAqB,CAErB,uBACF,CAEA,sBAEE,yBAA0B,CAD1B,UAEF,CAGA,mCAEE,wBAAkC,CAAlC,iCAAkC,CAElC,kBAAmB,CACnB,eAAgB,CAFhB,YAGF,CAEA,yCAEE,SAA0B,CAA1B,yBAA0B,CAE1B,cAAe,CADf,kBAEF,CAEA,yCAEE,eAAgB,CAChB,SACF,CAEA,yCAGE,+BAA4C,CAA5C,2CAA4C,CAC5C,UAA0B,CAA1B,yBAA0B,CAC1B,cAAe,CAHf,aAIF,CAEA,+DAEE,kBACF,CAGA,uBACE,wBAAkC,CAAlC,iCAAkC,CAElC,kBAAmB,CACnB,kBAAmB,CAFnB,YAGF,CAEA,0BACE,SAA0B,CAA1B,yBAA0B,CAC1B,kBACF,CAGA,gBACE,wBAAkC,CAAlC,iCAAkC,CAElC,kBAAmB,CADnB,YAAa,CAEb,iBACF,CAEA,YACE,wBAAsC,CAAtC,qCAAsC,CAEtC,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,cAAe,CAFf,iBAAkB,CAIlB,uBACF,CAEA,kBACE,wBAAyB,CACzB,0BACF,CAGA,SAEE,iBAAkB,CAGlB,eAAgB,CAFhB,aAAc,CAFd,YAAa,CAGb,iBAEF,CAEA,iBACE,wBAAsC,CAAtC,qCAAsC,CACtC,UACF,CAEA,eACE,wBAAoC,CAApC,mCAAoC,CACpC,UACF,CAGA,yBACE,iBAEE,eAAgB,CADhB,UAEF,CAEA,YACE,cACF,CAEA,SACE,cAAe,CACf,gBACF,CAEA,YAEE,QAAS,CADT,wDAEF,CAEA,cACE,YACF,CAEA,eACE,cACF,CACF,CAGA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,OACE,2BACF,CAEA,iBACE,GACE,kBACF,CACA,IACE,qBACF,CACA,GACE,kBACF,CACF,CAIA,mBAQE,kBAAmB,CAFnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,sBACE,wBAAqC,CAArC,oCAAqC,CAOrC,oBAAqC,CAArC,oCAAqC,CANrC,kBAAmB,CAOnB,gCAA0C,CAH1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAJhB,SAAU,CACV,UAMF,CAEA,kBAKE,kBAAmB,CAJnB,2CAAiE,CAAjE,2DAAiE,CACjE,UAAY,CAEZ,YAAa,CAEb,QAAS,CAHT,YAAa,CAIb,iBACF,CAEA,kBAKE,qBAAuB,CAFvB,iBAAkB,CADlB,WAAY,CAEZ,eAAgB,CAHhB,UAKF,CAEA,oBAKE,kBAAmB,CAFnB,kDAAqD,CAMrD,UAAY,CALZ,YAAa,CAGb,cAAe,CACf,eAAiB,CANjB,WAAY,CAIZ,sBAAuB,CALvB,UASF,CAEA,sBAEE,cAAe,CADf,cAEF,CAEA,iBAGE,cAAe,CAFf,cAAiB,CACjB,UAEF,CAEA,mBAGE,cAAe,CADf,eAAiB,CADjB,QAGF,CAEA,mBAEE,eAAgB,CAChB,eAAgB,CAFhB,YAGF,CAEA,kEAIE,kBACF,CAEA,8EAIE,SAA0B,CAA1B,yBAA0B,CAE1B,cAAe,CADf,kBAEF,CAEA,cAEE,kBAAmB,CAGnB,wBAAkC,CAAlC,iCAAkC,CAClC,kBAAmB,CALnB,YAAa,CAEb,QAAS,CACT,YAGF,CAEA,cACE,cACF,CAEA,cAIE,UAA0B,CAA1B,yBAA0B,CAH1B,QAAO,CACP,cAAe,CACf,eAEF,CAEA,aACE,oBAAqC,CAArC,oCAAqC,CAErC,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,cAAe,CAHf,gBAAiB,CAIjB,uBACF,CAEA,mBACE,qBAAyB,CACzB,0BACF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,aAEE,kBAAmB,CAGnB,wBAAkC,CAAlC,iCAAkC,CAClC,kBAAmB,CACnB,cAAe,CANf,YAAa,CAEb,QAAS,CACT,YAAa,CAIb,uBACF,CAEA,mBACE,wBAAiC,CAAjC,gCAAiC,CACjC,0BACF,CAEA,2BACE,yBACF,CAEA,4BACE,6BACF,CAEA,4BACE,0BACF,CAEA,aACE,cAAe,CAEf,iBAAkB,CADlB,UAEF,CAEA,aACE,QACF,CAEA,aAEE,UAA0B,CAA1B,yBAA0B,CAD1B,eAAiB,CAEjB,iBACF,CAEA,eACE,cAEF,CAEA,6BAHE,aAA4B,CAA5B,2BAMF,CAHA,cACE,cAEF,CAEA,aACE,YAAa,CACb,cAAe,CACf,OAAQ,CACR,eACF,CAEA,WACE,oBAAqC,CAArC,oCAAqC,CAGrC,kBAAmB,CAFnB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,cAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,wDAA2D,CAE3D,eACF,CAEA,cAGE,iBAAkB,CAElB,UAA0B,CAA1B,yBAA0B,CAD1B,cAAe,CAHf,WAKF,CAEA,gCANE,wBAAkC,CAAlC,iCAWF,CALA,kBAIE,4BAAyC,CAAzC,wCAAyC,CAFzC,YAAa,CACb,iBAEF,CAEA,oBAEE,aAA4B,CAA5B,2BAA4B,CAD5B,YAEF,CAGA,wBAEE,WAAY,CADZ,cAAe,CAEf,UAAW,CACX,YACF,CAEA,kBAEE,kBAAmB,CAEnB,wBAAqC,CAArC,oCAAqC,CAGrC,wBAAqC,CAArC,oCAAqC,CADrC,kBAAmB,CAGnB,aAA4B,CAA5B,2BAA4B,CAR5B,YAAa,CAOb,cAAe,CALf,QAAS,CAET,gBAKF,CAEA,aACE,UAA0B,CAA1B,yBACF,CAEA,iBACE,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAFlB,cAAe,CADf,cAAe,CAEf,WAAY,CAEZ,uBACF,CAEA,uBACE,wBAAiC,CAAjC,gCACF,CAGA,cAEE,wBAAkC,CAAlC,iCAAkC,CAClC,+BAA4C,CAA5C,2CAA4C,CAF5C,YAGF,CAEA,SAEE,eAAgB,CAChB,WAAY,CACZ,aAA4B,CAA5B,2BAA4B,CAE5B,cAAe,CALf,QAAO,CAOP,cAAe,CAHf,gBAAiB,CAEjB,uBAEF,CAEA,eACE,wBAAiC,CAAjC,gCAAiC,CACjC,UAA0B,CAA1B,yBACF,CAEA,gBACE,wBAAmC,CAAnC,kCAAmC,CAEnC,2BAA4C,CAA5C,2CAA4C,CAD5C,SAA0B,CAA1B,yBAEF,CAEA,gBAEE,2CAAiE,CAAjE,2DAAiE,CACjE,UAAY,CACZ,kBAAmB,CAHnB,iBAIF,CAEA,gBAME,qBAAuB,CAHvB,iBAAkB,CADlB,WAAY,CAEZ,kBAAmB,CACnB,eAAgB,CAJhB,UAMF,CAEA,oCAKE,kBAAmB,CAFnB,gBAAoC,CACpC,YAAa,CAGb,cAAe,CACf,eAAiB,CANjB,WAAY,CAIZ,sBAAuB,CALvB,UAQF,CAEA,cAGE,cAAe,CADf,cAAiB,CADjB,iBAGF,CAEA,qBAGE,cAAe,CADf,eAAkB,CAElB,UAAY,CAHZ,iBAIF,CAEA,qBAGE,cAAe,CAEf,eAAgB,CAHhB,eAAkB,CAElB,UAAY,CAHZ,iBAKF,CAEA,eAGE,qBAAuB,CAEvB,WAAY,CAEZ,iBAAkB,CAHlB,SAA0B,CAA1B,yBAA0B,CAK1B,cAAe,CARf,aAAc,CAOd,eAAiB,CAFjB,YAAa,CAIb,uBAAyB,CARzB,UASF,CAEA,qBACE,wBAAyB,CACzB,0BACF,CAEA,eAEE,+BAA4C,CAA5C,2CAA4C,CAD5C,iBAEF,CAEA,kBAEE,UAA0B,CAA1B,yBAA0B,CAC1B,cAAe,CAFf,QAGF,CAEA,gBAGE,wBAAkC,CAAlC,iCAAkC,CAFlC,4BAAyC,CAAzC,wCAAyC,CACzC,YAEF,CAEA,kCAEE,eAAgB,CAChB,WAAY,CAEZ,eAAgB,CADhB,SAAU,CAHV,eAAgB,CAKhB,iBACF,CAEA,oCAGE,aAA4B,CAA5B,2BAA4B,CAD5B,cAAe,CADf,YAGF,CAEA,yCACE,SAA0B,CAA1B,yBACF,CAGA,iBACE,YAAa,CACb,QAAS,CACT,sBAAuB,CACvB,eACF,CAEA,eACE,wBAA6B,CAE7B,oBAAqC,CAArC,oCAAqC,CAErC,iBAAkB,CAHlB,SAA0B,CAA1B,yBAA0B,CAM1B,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,iBAAkB,CAKlB,uBACF,CAEA,qBACE,oBAAqC,CAArC,oCAAqC,CACrC,UAAY,CACZ,0BACF,CAGA,yBACE,sBAEE,eAAgB,CADhB,UAEF,CAEA,kBAEE,qBAAsB,CADtB,YAAa,CAEb,iBACF,CAEA,mBACE,YACF,CAEA,cACE,QACF,CAEA,aACE,YACF,CAEA,cACE,yBACF,CAEA,iBAEE,kBAAmB,CADnB,qBAEF,CAEA,wBACE,WAAY,CACZ,UACF,CACF", "sources": ["App.css"], "sourcesContent": ["/* الخطوط والألوان الأساسية */\n:root {\n  --primary-bg: #1a1a1a;\n  --secondary-bg: #2d2d2d;\n  --accent-bg: #3d3d3d;\n  --primary-text: #ffffff;\n  --secondary-text: #b3b3b3;\n  --accent-color: #ff0000;\n  --border-color: #404040;\n  --hover-bg: #404040;\n  --success-color: #4caf50;\n  --warning-color: #ff9800;\n  --error-color: #f44336;\n}\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;\n  background-color: var(--primary-bg);\n  color: var(--primary-text);\n  direction: rtl;\n  overflow: hidden;\n}\n\n.app {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--primary-bg);\n}\n\n.app-content {\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n}\n\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--primary-bg);\n}\n\n/* شريط العنوان */\n.header {\n  background-color: var(--secondary-bg);\n  padding: 10px 20px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1px solid var(--border-color);\n  min-height: 60px;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-size: 18px;\n  font-weight: bold;\n  color: var(--accent-color);\n}\n\n.search-container {\n  display: flex;\n  align-items: center;\n  background-color: var(--accent-bg);\n  border-radius: 25px;\n  padding: 8px 15px;\n  min-width: 400px;\n}\n\n.search-input {\n  background: none;\n  border: none;\n  color: var(--primary-text);\n  font-size: 14px;\n  width: 100%;\n  outline: none;\n  direction: rtl;\n}\n\n.search-input::placeholder {\n  color: var(--secondary-text);\n}\n\n.search-btn {\n  background: none;\n  border: none;\n  color: var(--secondary-text);\n  cursor: pointer;\n  padding: 5px;\n  margin-left: 10px;\n}\n\n.search-btn:hover {\n  color: var(--primary-text);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.header-btn {\n  background: none;\n  border: none;\n  color: var(--secondary-text);\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n}\n\n.header-btn:hover {\n  background-color: var(--hover-bg);\n  color: var(--primary-text);\n}\n\n/* الشريط الجانبي */\n.sidebar {\n  width: 300px;\n  background-color: var(--secondary-bg);\n  border-left: 1px solid var(--border-color);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.sidebar-header {\n  padding: 15px;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.sidebar-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.sidebar-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.video-item {\n  display: flex;\n  gap: 10px;\n  padding: 10px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n  margin-bottom: 10px;\n}\n\n.video-item:hover {\n  background-color: var(--hover-bg);\n}\n\n.video-thumbnail {\n  width: 80px;\n  height: 60px;\n  border-radius: 5px;\n  object-fit: cover;\n}\n\n.video-info {\n  flex: 1;\n}\n\n.video-title {\n  font-size: 14px;\n  font-weight: 500;\n  margin-bottom: 5px;\n  line-height: 1.3;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.video-channel {\n  font-size: 12px;\n  color: var(--secondary-text);\n}\n\n/* مشغل يوتيوب */\n.youtube-player-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #000;\n  position: relative;\n}\n\n.youtube-player {\n  width: 100%;\n  height: 100%;\n}\n\n.player-placeholder {\n  text-align: center;\n  color: var(--secondary-text);\n}\n\n.player-placeholder h2 {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.player-placeholder p {\n  font-size: 16px;\n}\n\n/* نافذة الإعدادات */\n.settings-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.settings-modal {\n  background-color: var(--secondary-bg);\n  border-radius: 10px;\n  padding: 30px;\n  width: 500px;\n  max-width: 90vw;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.settings-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 25px;\n}\n\n.settings-title {\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: var(--secondary-text);\n  cursor: pointer;\n  font-size: 20px;\n  padding: 5px;\n}\n\n.close-btn:hover {\n  color: var(--primary-text);\n}\n\n.settings-section {\n  margin-bottom: 25px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 15px;\n  color: var(--accent-color);\n}\n\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: var(--accent-bg);\n  border-radius: 8px;\n}\n\n.setting-label {\n  font-size: 14px;\n}\n\n.setting-control select {\n  background-color: var(--primary-bg);\n  color: var(--primary-text);\n  border: 1px solid var(--border-color);\n  border-radius: 5px;\n  padding: 5px 10px;\n  font-size: 14px;\n}\n\n.toggle-switch {\n  position: relative;\n  width: 50px;\n  height: 25px;\n  background-color: var(--border-color);\n  border-radius: 25px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.toggle-switch.active {\n  background-color: var(--accent-color);\n}\n\n.toggle-switch::after {\n  content: '';\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  width: 21px;\n  height: 21px;\n  background-color: white;\n  border-radius: 50%;\n  transition: transform 0.3s ease;\n}\n\n.toggle-switch.active::after {\n  transform: translateX(-25px);\n}\n\n/* تحسينات للاستجابة */\n@media (max-width: 768px) {\n  .sidebar {\n    width: 250px;\n  }\n  \n  .search-container {\n    min-width: 250px;\n  }\n  \n  .settings-modal {\n    width: 90vw;\n    padding: 20px;\n  }\n}\n\n/* شريط التمرير المخصص */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--secondary-bg);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--border-color);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--hover-bg);\n}\n\n\n\n/* عناصر التحكم المخصصة للمشغل */\n.player-wrapper {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.custom-controls {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  padding: 15px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.player-wrapper:hover .custom-controls {\n  opacity: 1;\n}\n\n.controls-row {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  color: white;\n}\n\n.control-btn {\n  background: none;\n  border: none;\n  color: white;\n  cursor: pointer;\n  font-size: 18px;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n}\n\n.control-btn:hover {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.time-display {\n  font-size: 14px;\n  font-weight: 500;\n  min-width: 100px;\n  text-align: center;\n}\n\n.progress-container {\n  flex: 1;\n  margin: 0 10px;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n  outline: none;\n  cursor: pointer;\n}\n\n.progress-bar::-webkit-slider-thumb {\n  appearance: none;\n  width: 12px;\n  height: 12px;\n  background: var(--accent-color);\n  border-radius: 50%;\n  cursor: pointer;\n}\n\n.volume-container {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.volume-bar {\n  width: 80px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n  outline: none;\n  cursor: pointer;\n}\n\n.volume-bar::-webkit-slider-thumb {\n  appearance: none;\n  width: 10px;\n  height: 10px;\n  background: white;\n  border-radius: 50%;\n  cursor: pointer;\n}\n\n.video-info-overlay {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  background: rgba(0, 0, 0, 0.7);\n  padding: 10px 15px;\n  border-radius: 8px;\n  color: white;\n  max-width: 300px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.player-wrapper:hover .video-info-overlay {\n  opacity: 1;\n}\n\n.video-info-overlay h3 {\n  font-size: 14px;\n  margin-bottom: 5px;\n  line-height: 1.3;\n}\n\n.video-info-overlay p {\n  font-size: 12px;\n  color: #ccc;\n  margin: 0;\n}\n\n/* تحسينات الشريط الجانبي */\n.video-item {\n  position: relative;\n}\n\n.video-duration {\n  position: absolute;\n  bottom: 5px;\n  right: 5px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-size: 11px;\n  font-weight: 500;\n}\n\n.video-views {\n  font-size: 11px;\n  color: var(--secondary-text);\n  margin-top: 2px;\n}\n\n.video-published {\n  font-size: 11px;\n  color: var(--secondary-text);\n}\n\n/* تحسينات الإعدادات */\n.settings-section {\n  border-bottom: 1px solid var(--border-color);\n  padding-bottom: 20px;\n}\n\n.settings-section:last-child {\n  border-bottom: none;\n  padding-bottom: 0;\n}\n\n.quality-preview {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-top: 10px;\n  padding: 10px;\n  background-color: var(--primary-bg);\n  border-radius: 5px;\n}\n\n.quality-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background-color: var(--success-color);\n}\n\n.quality-description {\n  font-size: 12px;\n  color: var(--secondary-text);\n}\n\n/* حالات التحميل */\n.loading-spinner {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border: 2px solid var(--border-color);\n  border-radius: 50%;\n  border-top-color: var(--accent-color);\n  animation: spin 1s ease-in-out infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.error-message {\n  background-color: var(--error-color);\n  color: white;\n  padding: 10px;\n  border-radius: 5px;\n  margin: 10px 0;\n  text-align: center;\n}\n\n.success-message {\n  background-color: var(--success-color);\n  color: white;\n  padding: 10px;\n  border-radius: 5px;\n  margin: 10px 0;\n  text-align: center;\n}\n\n/* تحسينات الاستجابة للشاشات الصغيرة */\n@media (max-width: 768px) {\n  .controls-row {\n    gap: 8px;\n  }\n  \n  .time-display {\n    font-size: 12px;\n    min-width: 80px;\n  }\n  \n  .volume-container {\n    display: none;\n  }\n  \n  .video-info-overlay {\n    max-width: 200px;\n    font-size: 12px;\n  }\n}\n\n/* تأثيرات الانتقال */\n.fade-in {\n  animation: fadeIn 0.3s ease-in;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n.slide-up {\n  animation: slideUp 0.3s ease-out;\n}\n\n@keyframes slideUp {\n  from { transform: translateY(20px); opacity: 0; }\n  to { transform: translateY(0); opacity: 1; }\n}\n\n\n/* شريط الحالة */\n.status-bar {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  padding: 8px 20px;\n  background-color: var(--secondary-bg);\n  border-top: 1px solid var(--border-color);\n  font-size: 12px;\n  color: var(--secondary-text);\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n/* رسالة الترحيب */\n.welcome-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  animation: fadeIn 0.5s ease-in;\n}\n\n.welcome-content {\n  background-color: var(--secondary-bg);\n  border-radius: 15px;\n  padding: 40px;\n  text-align: center;\n  max-width: 500px;\n  border: 2px solid var(--accent-color);\n}\n\n.welcome-content h2 {\n  color: var(--accent-color);\n  margin-bottom: 20px;\n  font-size: 24px;\n}\n\n.welcome-content p {\n  margin-bottom: 20px;\n  color: var(--primary-text);\n}\n\n.welcome-content ul {\n  text-align: right;\n  margin: 20px 0;\n  padding-right: 20px;\n}\n\n.welcome-content li {\n  margin-bottom: 10px;\n  color: var(--primary-text);\n}\n\n.welcome-btn {\n  background-color: var(--accent-color);\n  color: white;\n  border: none;\n  padding: 12px 30px;\n  border-radius: 25px;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.welcome-btn:hover {\n  background-color: #cc0000;\n  transform: translateY(-2px);\n}\n\n/* تحسينات للفيديو النشط */\n.video-item.active {\n  background-color: var(--accent-color);\n  color: white;\n}\n\n.video-item.active .video-channel,\n.video-item.active .video-views,\n.video-item.active .video-published {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* ثيم فاتح (اختياري) */\n.app.light-theme {\n  --primary-bg: #ffffff;\n  --secondary-bg: #f5f5f5;\n  --accent-bg: #e0e0e0;\n  --primary-text: #000000;\n  --secondary-text: #666666;\n  --border-color: #cccccc;\n  --hover-bg: #e0e0e0;\n}\n\n/* تحسينات الأداء */\n.video-thumbnail {\n  transition: transform 0.2s ease;\n}\n\n.video-item:hover .video-thumbnail {\n  transform: scale(1.05);\n}\n\n/* مؤشرات التحميل المحسنة */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 15px;\n  padding: 30px;\n}\n\n.loading-text {\n  color: var(--secondary-text);\n  font-size: 14px;\n}\n\n/* تحسينات الإعدادات المتقدمة */\n.advanced-settings {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid var(--border-color);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.stat-card {\n  background-color: var(--accent-bg);\n  padding: 15px;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: var(--accent-color);\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: var(--secondary-text);\n}\n\n/* تأثيرات الانتقال المحسنة */\n.slide-in-right {\n  animation: slideInRight 0.3s ease-out;\n}\n\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n.bounce-in {\n  animation: bounceIn 0.5s ease-out;\n}\n\n@keyframes bounceIn {\n  0% {\n    transform: scale(0.3);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  70% {\n    transform: scale(0.9);\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* تحسينات إمكانية الوصول */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n/* تحسينات للطباعة */\n@media print {\n  .sidebar,\n  .header,\n  .status-bar,\n  .settings-overlay {\n    display: none;\n  }\n  \n  .main-content {\n    width: 100%;\n  }\n}\n\n\n/* لوحة تحكم المطور */\n.developer-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n}\n\n.developer-panel {\n  background-color: var(--secondary-bg);\n  border-radius: 15px;\n  padding: 0;\n  width: 90vw;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow: hidden;\n  border: 2px solid var(--accent-color);\n}\n\n.panel-header {\n  background-color: var(--accent-color);\n  color: white;\n  padding: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h2 {\n  margin: 0;\n  font-size: 20px;\n}\n\n.panel-tabs {\n  display: flex;\n  background-color: var(--accent-bg);\n  border-bottom: 1px solid var(--border-color);\n}\n\n.tab-btn {\n  flex: 1;\n  background: none;\n  border: none;\n  color: var(--secondary-text);\n  padding: 15px 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.tab-btn:hover {\n  background-color: var(--hover-bg);\n  color: var(--primary-text);\n}\n\n.tab-btn.active {\n  background-color: var(--primary-bg);\n  color: var(--accent-color);\n  border-bottom: 2px solid var(--accent-color);\n}\n\n.tab-content {\n  padding: 20px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n/* إحصائيات الأرباح */\n.revenue-overview {\n  margin-bottom: 30px;\n}\n\n.revenue-card {\n  background: linear-gradient(135deg, var(--accent-color), #cc0000);\n  color: white;\n  padding: 25px;\n  border-radius: 15px;\n  text-align: center;\n  margin-bottom: 20px;\n}\n\n.revenue-card.main {\n  font-size: 18px;\n}\n\n.revenue-icon {\n  font-size: 32px;\n  margin-bottom: 10px;\n}\n\n.revenue-value {\n  font-size: 28px;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n\n.revenue-label {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n/* شبكة الإحصائيات */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n  margin-bottom: 30px;\n}\n\n/* نماذج الإضافة */\n.add-ad-section,\n.add-affiliate-section {\n  background-color: var(--accent-bg);\n  padding: 20px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: var(--primary-text);\n}\n\n.form-group input,\n.form-group textarea,\n.form-group select {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid var(--border-color);\n  border-radius: 5px;\n  background-color: var(--primary-bg);\n  color: var(--primary-text);\n  font-size: 14px;\n}\n\n.form-group textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n\n.add-btn {\n  background-color: var(--accent-color);\n  color: white;\n  border: none;\n  padding: 12px 25px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.add-btn:hover {\n  background-color: #cc0000;\n  transform: translateY(-2px);\n}\n\n/* معاينة الإعلان */\n.ad-preview {\n  background-color: var(--accent-bg);\n  padding: 20px;\n  border-radius: 10px;\n  margin-top: 20px;\n}\n\n.preview-ad {\n  border: 2px dashed var(--border-color);\n  padding: 15px;\n  border-radius: 8px;\n  background-color: var(--primary-bg);\n}\n\n.ad-content {\n  position: relative;\n  padding: 15px;\n  background-color: var(--secondary-bg);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.ad-content:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n}\n\n.ad-text h5 {\n  margin: 0 0 8px 0;\n  color: var(--primary-text);\n  font-size: 16px;\n}\n\n.ad-text p {\n  margin: 0;\n  color: var(--secondary-text);\n  font-size: 14px;\n  line-height: 1.4;\n}\n\n.ad-label {\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  background-color: var(--accent-color);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-size: 10px;\n  font-weight: bold;\n}\n\n/* الإعلانات المدمجة */\n.integrated-banner-ad {\n  margin: 15px 0;\n  border-radius: 8px;\n  overflow: hidden;\n  animation: slideUp 0.5s ease-out;\n}\n\n.integrated-sidebar-ad {\n  margin: 10px 0;\n  border-radius: 8px;\n  overflow: hidden;\n  font-size: 12px;\n}\n\n.integrated-sidebar-ad .ad-content {\n  padding: 10px;\n}\n\n.integrated-sidebar-ad .ad-text h5 {\n  font-size: 13px;\n  margin-bottom: 5px;\n}\n\n.integrated-sidebar-ad .ad-text p {\n  font-size: 11px;\n}\n\n/* الروابط التابعة */\n.affiliate-link {\n  color: var(--accent-color);\n  text-decoration: none;\n  border-bottom: 1px dotted var(--accent-color);\n  transition: all 0.3s ease;\n}\n\n.affiliate-link:hover {\n  color: #cc0000;\n  border-bottom-style: solid;\n}\n\n/* نصائح الربح */\n.monetization-tips,\n.affiliate-info {\n  background-color: var(--accent-bg);\n  padding: 20px;\n  border-radius: 10px;\n  margin-top: 20px;\n}\n\n.monetization-tips h4,\n.affiliate-info h4 {\n  color: var(--accent-color);\n  margin-bottom: 15px;\n  font-size: 16px;\n}\n\n.monetization-tips ul,\n.affiliate-info ul {\n  list-style: none;\n  padding: 0;\n}\n\n.monetization-tips li,\n.affiliate-info li {\n  padding: 8px 0;\n  border-bottom: 1px solid var(--border-color);\n  color: var(--primary-text);\n  font-size: 14px;\n}\n\n.monetization-tips li:last-child,\n.affiliate-info li:last-child {\n  border-bottom: none;\n}\n\n/* إعدادات الربح */\n.monetization-settings {\n  background-color: var(--accent-bg);\n  padding: 20px;\n  border-radius: 10px;\n  margin-bottom: 20px;\n}\n\n.monetization-settings h4 {\n  color: var(--accent-color);\n  margin-bottom: 20px;\n}\n\n/* تصدير البيانات */\n.export-section {\n  background-color: var(--accent-bg);\n  padding: 20px;\n  border-radius: 10px;\n  text-align: center;\n}\n\n.export-btn {\n  background-color: var(--success-color);\n  color: white;\n  border: none;\n  padding: 12px 25px;\n  border-radius: 8px;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.export-btn:hover {\n  background-color: #45a049;\n  transform: translateY(-2px);\n}\n\n/* رسائل النجاح والخطأ */\n.message {\n  padding: 12px;\n  border-radius: 8px;\n  margin: 15px 0;\n  text-align: center;\n  font-weight: 500;\n}\n\n.message.success {\n  background-color: var(--success-color);\n  color: white;\n}\n\n.message.error {\n  background-color: var(--error-color);\n  color: white;\n}\n\n/* تحسينات الاستجابة */\n@media (max-width: 768px) {\n  .developer-panel {\n    width: 95vw;\n    max-height: 95vh;\n  }\n  \n  .panel-tabs {\n    flex-wrap: wrap;\n  }\n  \n  .tab-btn {\n    font-size: 12px;\n    padding: 12px 8px;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n    gap: 10px;\n  }\n  \n  .revenue-card {\n    padding: 20px;\n  }\n  \n  .revenue-value {\n    font-size: 24px;\n  }\n}\n\n/* تأثيرات الحركة */\n@keyframes slideUp {\n  from {\n    transform: translateY(20px);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n\n/* معلومات المطور */\n.developer-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.9);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 3000;\n}\n\n.developer-info-modal {\n  background-color: var(--secondary-bg);\n  border-radius: 15px;\n  padding: 0;\n  width: 90vw;\n  max-width: 600px;\n  max-height: 90vh;\n  overflow: hidden;\n  border: 2px solid var(--accent-color);\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n}\n\n.developer-header {\n  background: linear-gradient(135deg, var(--accent-color), #cc0000);\n  color: white;\n  padding: 25px;\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  position: relative;\n}\n\n.developer-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  overflow: hidden;\n  border: 3px solid white;\n}\n\n.avatar-placeholder {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #4a90e2, #357abd);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  font-weight: bold;\n  color: white;\n}\n\n.developer-details h2 {\n  margin: 0 0 5px 0;\n  font-size: 24px;\n}\n\n.developer-title {\n  margin: 0 0 5px 0;\n  opacity: 0.9;\n  font-size: 14px;\n}\n\n.developer-channel {\n  margin: 0;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.developer-content {\n  padding: 25px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.contact-section,\n.social-section,\n.about-section,\n.app-info-section {\n  margin-bottom: 25px;\n}\n\n.contact-section h3,\n.social-section h3,\n.about-section h3,\n.app-info-section h3 {\n  color: var(--accent-color);\n  margin-bottom: 15px;\n  font-size: 18px;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  background-color: var(--accent-bg);\n  border-radius: 10px;\n}\n\n.contact-icon {\n  font-size: 24px;\n}\n\n.contact-text {\n  flex: 1;\n  font-size: 16px;\n  font-weight: bold;\n  color: var(--primary-text);\n}\n\n.contact-btn {\n  background-color: var(--accent-color);\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.contact-btn:hover {\n  background-color: #cc0000;\n  transform: translateY(-2px);\n}\n\n.social-links {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.social-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  background-color: var(--accent-bg);\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.social-item:hover {\n  background-color: var(--hover-bg);\n  transform: translateX(-5px);\n}\n\n.social-item.youtube:hover {\n  border-left: 4px solid #ff0000;\n}\n\n.social-item.facebook:hover {\n  border-left: 4px solid #1877f2;\n}\n\n.social-item.telegram:hover {\n  border-left: 4px solid #0088cc;\n}\n\n.social-icon {\n  font-size: 24px;\n  width: 40px;\n  text-align: center;\n}\n\n.social-info {\n  flex: 1;\n}\n\n.social-name {\n  font-weight: bold;\n  color: var(--primary-text);\n  margin-bottom: 3px;\n}\n\n.social-handle {\n  font-size: 12px;\n  color: var(--secondary-text);\n}\n\n.social-arrow {\n  font-size: 18px;\n  color: var(--secondary-text);\n}\n\n.skills-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 15px;\n}\n\n.skill-tag {\n  background-color: var(--accent-color);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.app-features {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 8px;\n  margin-top: 15px;\n}\n\n.feature-item {\n  padding: 8px;\n  background-color: var(--accent-bg);\n  border-radius: 6px;\n  font-size: 14px;\n  color: var(--primary-text);\n}\n\n.developer-footer {\n  background-color: var(--accent-bg);\n  padding: 20px;\n  text-align: center;\n  border-top: 1px solid var(--border-color);\n}\n\n.developer-footer p {\n  margin: 5px 0;\n  color: var(--secondary-text);\n}\n\n/* معلومات المطور المدمجة */\n.developer-info-compact {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  z-index: 1000;\n}\n\n.developer-credit {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  background-color: var(--secondary-bg);\n  padding: 8px 12px;\n  border-radius: 20px;\n  border: 1px solid var(--border-color);\n  font-size: 12px;\n  color: var(--secondary-text);\n}\n\n.credit-text {\n  color: var(--primary-text);\n}\n\n.info-toggle-btn {\n  background: none;\n  border: none;\n  font-size: 16px;\n  cursor: pointer;\n  padding: 2px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.info-toggle-btn:hover {\n  background-color: var(--hover-bg);\n}\n\n/* تحسينات الشريط الجانبي */\n.sidebar-tabs {\n  display: flex;\n  background-color: var(--accent-bg);\n  border-bottom: 1px solid var(--border-color);\n}\n\n.tab-btn {\n  flex: 1;\n  background: none;\n  border: none;\n  color: var(--secondary-text);\n  padding: 12px 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 13px;\n}\n\n.tab-btn:hover {\n  background-color: var(--hover-bg);\n  color: var(--primary-text);\n}\n\n.tab-btn.active {\n  background-color: var(--primary-bg);\n  color: var(--accent-color);\n  border-bottom: 2px solid var(--accent-color);\n}\n\n.channel-header {\n  padding: 20px 15px;\n  background: linear-gradient(135deg, var(--accent-color), #cc0000);\n  color: white;\n  margin-bottom: 15px;\n}\n\n.channel-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  margin: 0 auto 15px;\n  overflow: hidden;\n  border: 3px solid white;\n}\n\n.channel-avatar .avatar-placeholder {\n  width: 100%;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.channel-name {\n  text-align: center;\n  margin: 0 0 5px 0;\n  font-size: 18px;\n}\n\n.channel-subscribers {\n  text-align: center;\n  margin: 0 0 10px 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.channel-description {\n  text-align: center;\n  margin: 0 0 15px 0;\n  font-size: 12px;\n  opacity: 0.8;\n  line-height: 1.4;\n}\n\n.subscribe-btn {\n  display: block;\n  width: 100%;\n  background-color: white;\n  color: var(--accent-color);\n  border: none;\n  padding: 10px;\n  border-radius: 6px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.subscribe-btn:hover {\n  background-color: #f0f0f0;\n  transform: translateY(-2px);\n}\n\n.section-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.section-title h4 {\n  margin: 0;\n  color: var(--primary-text);\n  font-size: 16px;\n}\n\n.sidebar-footer {\n  border-top: 1px solid var(--border-color);\n  padding: 15px;\n  background-color: var(--accent-bg);\n}\n\n.sidebar-footer .developer-credit {\n  position: static;\n  background: none;\n  border: none;\n  padding: 0;\n  border-radius: 0;\n  text-align: center;\n}\n\n.sidebar-footer .developer-credit p {\n  margin: 3px 0;\n  font-size: 11px;\n  color: var(--secondary-text);\n}\n\n.sidebar-footer .developer-credit strong {\n  color: var(--accent-color);\n}\n\n/* تحسينات رسالة الترحيب */\n.welcome-buttons {\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n.developer-btn {\n  background-color: transparent;\n  color: var(--accent-color);\n  border: 2px solid var(--accent-color);\n  padding: 12px 25px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.developer-btn:hover {\n  background-color: var(--accent-color);\n  color: white;\n  transform: translateY(-2px);\n}\n\n/* تحسينات الاستجابة */\n@media (max-width: 768px) {\n  .developer-info-modal {\n    width: 95vw;\n    max-height: 95vh;\n  }\n  \n  .developer-header {\n    padding: 20px;\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .developer-content {\n    padding: 20px;\n  }\n  \n  .social-links {\n    gap: 10px;\n  }\n  \n  .social-item {\n    padding: 12px;\n  }\n  \n  .app-features {\n    grid-template-columns: 1fr;\n  }\n  \n  .welcome-buttons {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .developer-info-compact {\n    bottom: 10px;\n    right: 10px;\n  }\n}\n\n"], "names": [], "sourceRoot": ""}