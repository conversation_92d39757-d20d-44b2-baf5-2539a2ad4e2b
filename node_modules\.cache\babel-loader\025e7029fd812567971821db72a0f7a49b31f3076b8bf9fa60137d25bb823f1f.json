{"ast": null, "code": "import React,{useEffect,useRef,useState}from'react';import{YouTubeHelper}from'../utils/youtubeApi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const YouTubePlayerEnhanced=_ref=>{let{videoId,settings}=_ref;const playerRef=useRef(null);const playerInstanceRef=useRef(null);const[playerState,setPlayerState]=useState('unstarted');const[currentTime,setCurrentTime]=useState(0);const[duration,setDuration]=useState(0);const[volume,setVolume]=useState(100);const[isFullscreen,setIsFullscreen]=useState(false);const[videoInfo,setVideoInfo]=useState(null);const[availableQualities,setAvailableQualities]=useState([]);useEffect(()=>{// تحميل YouTube IFrame API\nif(!window.YT){const script=document.createElement('script');script.src='https://www.youtube.com/iframe_api';script.async=true;document.body.appendChild(script);window.onYouTubeIframeAPIReady=()=>{initializePlayer();};}else{initializePlayer();}return()=>{if(playerInstanceRef.current){try{playerInstanceRef.current.destroy();}catch(error){console.log('Error destroying player:',error);}}};},[]);useEffect(()=>{if(playerInstanceRef.current&&videoId){loadVideo(videoId);}},[videoId]);useEffect(()=>{if(playerInstanceRef.current&&settings){applySettings();}},[settings]);const initializePlayer=()=>{if(playerRef.current&&window.YT&&window.YT.Player){playerInstanceRef.current=new window.YT.Player(playerRef.current,{height:'100%',width:'100%',videoId:videoId||'',playerVars:{autoplay:0,controls:settings!==null&&settings!==void 0&&settings.hideControls?0:1,disablekb:settings!==null&&settings!==void 0&&settings.hideControls?1:0,fs:1,iv_load_policy:3,modestbranding:1,playsinline:1,rel:0,showinfo:0,cc_load_policy:0,hl:'ar',cc_lang_pref:'ar',origin:window.location.origin},events:{onReady:onPlayerReady,onStateChange:onPlayerStateChange,onError:onPlayerError}});}};const onPlayerReady=event=>{console.log('YouTube Player Ready');// الحصول على الجودات المتاحة\nconst qualities=event.target.getAvailableQualityLevels();setAvailableQualities(qualities);// تطبيق الإعدادات الأولية\napplySettings();// بدء تحديث الوقت\nstartTimeUpdater();};const onPlayerStateChange=event=>{const states={'-1':'unstarted','0':'ended','1':'playing','2':'paused','3':'buffering','5':'cued'};const newState=states[event.data]||'unknown';setPlayerState(newState);if(newState==='playing'){setDuration(event.target.getDuration());}};const onPlayerError=event=>{console.error('YouTube Player Error:',event.data);const errorMessages={2:'معرف الفيديو غير صحيح',5:'خطأ في تشغيل الفيديو',100:'الفيديو غير موجود أو محذوف',101:'الفيديو غير متاح في منطقتك',150:'الفيديو غير متاح في منطقتك'};const errorMessage=errorMessages[event.data]||'خطأ غير معروف في تشغيل الفيديو';alert(errorMessage);};const loadVideo=async videoId=>{if(playerInstanceRef.current){try{playerInstanceRef.current.loadVideoById(videoId);// الحصول على معلومات الفيديو\nconst info=await YouTubeHelper.getVideoInfo(videoId);setVideoInfo(info);}catch(error){console.error('Error loading video:',error);}}};const applySettings=()=>{if(!playerInstanceRef.current)return;try{// تطبيق إعدادات الجودة\nif(settings!==null&&settings!==void 0&&settings.quality&&settings.quality!=='auto'){playerInstanceRef.current.setPlaybackQuality(settings.quality);}// تطبيق إعدادات الصوت\nif((settings===null||settings===void 0?void 0:settings.volume)!==undefined){playerInstanceRef.current.setVolume(settings.volume);setVolume(settings.volume);}}catch(error){console.error('Error applying settings:',error);}};const startTimeUpdater=()=>{const updateTime=()=>{if(playerInstanceRef.current&&playerState==='playing'){try{const time=playerInstanceRef.current.getCurrentTime();setCurrentTime(time);}catch(error){console.error('Error getting current time:',error);}}};const interval=setInterval(updateTime,1000);return()=>clearInterval(interval);};// عناصر التحكم المخصصة\nconst playPause=()=>{if(!playerInstanceRef.current)return;if(playerState==='playing'){playerInstanceRef.current.pauseVideo();}else{playerInstanceRef.current.playVideo();}};const seekTo=time=>{if(playerInstanceRef.current){playerInstanceRef.current.seekTo(time,true);}};const changeVolume=newVolume=>{if(playerInstanceRef.current){playerInstanceRef.current.setVolume(newVolume);setVolume(newVolume);}};const toggleFullscreen=()=>{if(!document.fullscreenElement){var _playerRef$current,_playerRef$current$pa;(_playerRef$current=playerRef.current)===null||_playerRef$current===void 0?void 0:(_playerRef$current$pa=_playerRef$current.parentElement)===null||_playerRef$current$pa===void 0?void 0:_playerRef$current$pa.requestFullscreen();setIsFullscreen(true);}else{document.exitFullscreen();setIsFullscreen(false);}};const formatTime=seconds=>{const mins=Math.floor(seconds/60);const secs=Math.floor(seconds%60);return`${mins}:${secs.toString().padStart(2,'0')}`;};if(!videoId){return/*#__PURE__*/_jsx(\"div\",{className:\"youtube-player-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"player-placeholder\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0645\\u0634\\u063A\\u0644 \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u0623\\u0648 \\u0627\\u0644\\u0635\\u0642 \\u0631\\u0627\\u0628\\u0637 \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628 \\u0644\\u0628\\u062F\\u0621 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'48px',margin:'20px 0'},children:\"\\uD83D\\uDCFA\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0627\\u0633\\u062A\\u0645\\u062A\\u0639 \\u0628\\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628 \\u0628\\u062F\\u0648\\u0646 \\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0645\\u0639 \\u0648\\u0627\\u062C\\u0647\\u0629 \\u0639\\u0631\\u0628\\u064A\\u0629 \\u0645\\u0631\\u064A\\u062D\\u0629\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'30px',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u0627\\u0644\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A:\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{listStyle:'none',padding:0,marginTop:'15px'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDEAB \\u0645\\u0627\\u0646\\u0639 \\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0642\\u0648\\u064A\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83C\\uDFA8 \\u0648\\u0627\\u062C\\u0647\\u0629 \\u0639\\u0631\\u0628\\u064A\\u0629 \\u0645\\u0639 \\u062B\\u064A\\u0645 \\u062F\\u0627\\u0643\\u0646\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2699\\uFE0F \\u062A\\u062D\\u0643\\u0645 \\u0643\\u0627\\u0645\\u0644 \\u0641\\u064A \\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDD0D \\u0628\\u062D\\u062B \\u0633\\u0631\\u064A\\u0639 \\u0641\\u064A \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDCF1 \\u062A\\u0635\\u0645\\u064A\\u0645 \\u0645\\u062A\\u062C\\u0627\\u0648\\u0628\"})]})]})]})});}return/*#__PURE__*/_jsx(\"div\",{className:\"youtube-player-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"player-wrapper\",children:[/*#__PURE__*/_jsx(\"div\",{ref:playerRef,className:\"youtube-player\",id:\"youtube-player\"}),!(settings!==null&&settings!==void 0&&settings.hideControls)&&/*#__PURE__*/_jsx(\"div\",{className:\"custom-controls\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"controls-row\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"control-btn\",onClick:playPause,title:playerState==='playing'?'إيقاف مؤقت':'تشغيل',children:playerState==='playing'?'⏸️':'▶️'}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-display\",children:[formatTime(currentTime),\" / \",formatTime(duration)]}),/*#__PURE__*/_jsx(\"div\",{className:\"progress-container\",children:/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"0\",max:duration,value:currentTime,onChange:e=>seekTo(parseFloat(e.target.value)),className:\"progress-bar\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"volume-container\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDD0A\"}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"0\",max:\"100\",value:volume,onChange:e=>changeVolume(parseInt(e.target.value)),className:\"volume-bar\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"control-btn\",onClick:toggleFullscreen,title:\"\\u0645\\u0644\\u0621 \\u0627\\u0644\\u0634\\u0627\\u0634\\u0629\",children:isFullscreen?'🗗':'🗖'})]})}),videoInfo&&/*#__PURE__*/_jsxs(\"div\",{className:\"video-info-overlay\",children:[/*#__PURE__*/_jsx(\"h3\",{children:videoInfo.title}),/*#__PURE__*/_jsx(\"p\",{children:videoInfo.channel})]})]})});};export default YouTubePlayerEnhanced;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "YouTubeHelper", "jsx", "_jsx", "jsxs", "_jsxs", "YouTubePlayerEnhanced", "_ref", "videoId", "settings", "playerRef", "playerInstanceRef", "playerState", "setPlayerState", "currentTime", "setCurrentTime", "duration", "setDuration", "volume", "setVolume", "isFullscreen", "setIsFullscreen", "videoInfo", "setVideoInfo", "availableQualities", "setAvailableQualities", "window", "YT", "script", "document", "createElement", "src", "async", "body", "append<PERSON><PERSON><PERSON>", "onYouTubeIframeAPIReady", "initializePlayer", "current", "destroy", "error", "console", "log", "loadVideo", "applySettings", "Player", "height", "width", "playerVars", "autoplay", "controls", "hideControls", "disablekb", "fs", "iv_load_policy", "modestbranding", "playsinline", "rel", "showinfo", "cc_load_policy", "hl", "cc_lang_pref", "origin", "location", "events", "onReady", "onPlayerReady", "onStateChange", "onPlayerStateChange", "onError", "onPlayerError", "event", "qualities", "target", "getAvailableQualityLevels", "startTimeUpdater", "states", "newState", "data", "getDuration", "errorMessages", "errorMessage", "alert", "loadVideoById", "info", "getVideoInfo", "quality", "setPlaybackQuality", "undefined", "updateTime", "time", "getCurrentTime", "interval", "setInterval", "clearInterval", "playPause", "pauseVideo", "playVideo", "seekTo", "changeVolume", "newVolume", "toggleFullscreen", "fullscreenElement", "_playerRef$current", "_playerRef$current$pa", "parentElement", "requestFullscreen", "exitFullscreen", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "className", "children", "style", "fontSize", "margin", "marginTop", "textAlign", "listStyle", "padding", "ref", "id", "onClick", "title", "type", "min", "max", "value", "onChange", "e", "parseFloat", "parseInt", "channel"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/components/YouTubePlayerEnhanced.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { YouTubeHelper } from '../utils/youtubeApi';\n\nconst YouTubePlayerEnhanced = ({ videoId, settings }) => {\n  const playerRef = useRef(null);\n  const playerInstanceRef = useRef(null);\n  const [playerState, setPlayerState] = useState('unstarted');\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(100);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [videoInfo, setVideoInfo] = useState(null);\n  const [availableQualities, setAvailableQualities] = useState([]);\n\n  useEffect(() => {\n    // تحميل YouTube IFrame API\n    if (!window.YT) {\n      const script = document.createElement('script');\n      script.src = 'https://www.youtube.com/iframe_api';\n      script.async = true;\n      document.body.appendChild(script);\n\n      window.onYouTubeIframeAPIReady = () => {\n        initializePlayer();\n      };\n    } else {\n      initializePlayer();\n    }\n\n    return () => {\n      if (playerInstanceRef.current) {\n        try {\n          playerInstanceRef.current.destroy();\n        } catch (error) {\n          console.log('Error destroying player:', error);\n        }\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    if (playerInstanceRef.current && videoId) {\n      loadVideo(videoId);\n    }\n  }, [videoId]);\n\n  useEffect(() => {\n    if (playerInstanceRef.current && settings) {\n      applySettings();\n    }\n  }, [settings]);\n\n  const initializePlayer = () => {\n    if (playerRef.current && window.YT && window.YT.Player) {\n      playerInstanceRef.current = new window.YT.Player(playerRef.current, {\n        height: '100%',\n        width: '100%',\n        videoId: videoId || '',\n        playerVars: {\n          autoplay: 0,\n          controls: settings?.hideControls ? 0 : 1,\n          disablekb: settings?.hideControls ? 1 : 0,\n          fs: 1,\n          iv_load_policy: 3,\n          modestbranding: 1,\n          playsinline: 1,\n          rel: 0,\n          showinfo: 0,\n          cc_load_policy: 0,\n          hl: 'ar',\n          cc_lang_pref: 'ar',\n          origin: window.location.origin\n        },\n        events: {\n          onReady: onPlayerReady,\n          onStateChange: onPlayerStateChange,\n          onError: onPlayerError\n        }\n      });\n    }\n  };\n\n  const onPlayerReady = (event) => {\n    console.log('YouTube Player Ready');\n    \n    // الحصول على الجودات المتاحة\n    const qualities = event.target.getAvailableQualityLevels();\n    setAvailableQualities(qualities);\n    \n    // تطبيق الإعدادات الأولية\n    applySettings();\n    \n    // بدء تحديث الوقت\n    startTimeUpdater();\n  };\n\n  const onPlayerStateChange = (event) => {\n    const states = {\n      '-1': 'unstarted',\n      '0': 'ended',\n      '1': 'playing',\n      '2': 'paused',\n      '3': 'buffering',\n      '5': 'cued'\n    };\n    \n    const newState = states[event.data] || 'unknown';\n    setPlayerState(newState);\n    \n    if (newState === 'playing') {\n      setDuration(event.target.getDuration());\n    }\n  };\n\n  const onPlayerError = (event) => {\n    console.error('YouTube Player Error:', event.data);\n    const errorMessages = {\n      2: 'معرف الفيديو غير صحيح',\n      5: 'خطأ في تشغيل الفيديو',\n      100: 'الفيديو غير موجود أو محذوف',\n      101: 'الفيديو غير متاح في منطقتك',\n      150: 'الفيديو غير متاح في منطقتك'\n    };\n    \n    const errorMessage = errorMessages[event.data] || 'خطأ غير معروف في تشغيل الفيديو';\n    alert(errorMessage);\n  };\n\n  const loadVideo = async (videoId) => {\n    if (playerInstanceRef.current) {\n      try {\n        playerInstanceRef.current.loadVideoById(videoId);\n        \n        // الحصول على معلومات الفيديو\n        const info = await YouTubeHelper.getVideoInfo(videoId);\n        setVideoInfo(info);\n      } catch (error) {\n        console.error('Error loading video:', error);\n      }\n    }\n  };\n\n  const applySettings = () => {\n    if (!playerInstanceRef.current) return;\n\n    try {\n      // تطبيق إعدادات الجودة\n      if (settings?.quality && settings.quality !== 'auto') {\n        playerInstanceRef.current.setPlaybackQuality(settings.quality);\n      }\n\n      // تطبيق إعدادات الصوت\n      if (settings?.volume !== undefined) {\n        playerInstanceRef.current.setVolume(settings.volume);\n        setVolume(settings.volume);\n      }\n    } catch (error) {\n      console.error('Error applying settings:', error);\n    }\n  };\n\n  const startTimeUpdater = () => {\n    const updateTime = () => {\n      if (playerInstanceRef.current && playerState === 'playing') {\n        try {\n          const time = playerInstanceRef.current.getCurrentTime();\n          setCurrentTime(time);\n        } catch (error) {\n          console.error('Error getting current time:', error);\n        }\n      }\n    };\n\n    const interval = setInterval(updateTime, 1000);\n    return () => clearInterval(interval);\n  };\n\n  // عناصر التحكم المخصصة\n  const playPause = () => {\n    if (!playerInstanceRef.current) return;\n    \n    if (playerState === 'playing') {\n      playerInstanceRef.current.pauseVideo();\n    } else {\n      playerInstanceRef.current.playVideo();\n    }\n  };\n\n  const seekTo = (time) => {\n    if (playerInstanceRef.current) {\n      playerInstanceRef.current.seekTo(time, true);\n    }\n  };\n\n  const changeVolume = (newVolume) => {\n    if (playerInstanceRef.current) {\n      playerInstanceRef.current.setVolume(newVolume);\n      setVolume(newVolume);\n    }\n  };\n\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      playerRef.current?.parentElement?.requestFullscreen();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen();\n      setIsFullscreen(false);\n    }\n  };\n\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = Math.floor(seconds % 60);\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  if (!videoId) {\n    return (\n      <div className=\"youtube-player-container\">\n        <div className=\"player-placeholder\">\n          <h2>مرحباً بك في مشغل يوتيوب</h2>\n          <p>ابحث عن فيديو أو الصق رابط يوتيوب لبدء المشاهدة</p>\n          <div style={{ fontSize: '48px', margin: '20px 0' }}>📺</div>\n          <p>استمتع بمشاهدة يوتيوب بدون إعلانات مع واجهة عربية مريحة</p>\n          \n          <div style={{ marginTop: '30px', textAlign: 'center' }}>\n            <h3>المميزات:</h3>\n            <ul style={{ listStyle: 'none', padding: 0, marginTop: '15px' }}>\n              <li>🚫 مانع إعلانات قوي</li>\n              <li>🎨 واجهة عربية مع ثيم داكن</li>\n              <li>⚙️ تحكم كامل في جودة الفيديو</li>\n              <li>🔍 بحث سريع في يوتيوب</li>\n              <li>📱 تصميم متجاوب</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"youtube-player-container\">\n      <div className=\"player-wrapper\">\n        <div \n          ref={playerRef}\n          className=\"youtube-player\"\n          id=\"youtube-player\"\n        />\n        \n        {/* عناصر التحكم المخصصة */}\n        {!settings?.hideControls && (\n          <div className=\"custom-controls\">\n            <div className=\"controls-row\">\n              <button \n                className=\"control-btn\"\n                onClick={playPause}\n                title={playerState === 'playing' ? 'إيقاف مؤقت' : 'تشغيل'}\n              >\n                {playerState === 'playing' ? '⏸️' : '▶️'}\n              </button>\n              \n              <div className=\"time-display\">\n                {formatTime(currentTime)} / {formatTime(duration)}\n              </div>\n              \n              <div className=\"progress-container\">\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max={duration}\n                  value={currentTime}\n                  onChange={(e) => seekTo(parseFloat(e.target.value))}\n                  className=\"progress-bar\"\n                />\n              </div>\n              \n              <div className=\"volume-container\">\n                <span>🔊</span>\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max=\"100\"\n                  value={volume}\n                  onChange={(e) => changeVolume(parseInt(e.target.value))}\n                  className=\"volume-bar\"\n                />\n              </div>\n              \n              <button \n                className=\"control-btn\"\n                onClick={toggleFullscreen}\n                title=\"ملء الشاشة\"\n              >\n                {isFullscreen ? '🗗' : '🗖'}\n              </button>\n            </div>\n          </div>\n        )}\n        \n        {/* معلومات الفيديو */}\n        {videoInfo && (\n          <div className=\"video-info-overlay\">\n            <h3>{videoInfo.title}</h3>\n            <p>{videoInfo.channel}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default YouTubePlayerEnhanced;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,aAAa,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,qBAAqB,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,OAAO,CAAEC,QAAS,CAAC,CAAAF,IAAA,CAClD,KAAM,CAAAG,SAAS,CAAGX,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAY,iBAAiB,CAAGZ,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAACa,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAAC,WAAW,CAAC,CAC3D,KAAM,CAACc,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACgB,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACkB,MAAM,CAAEC,SAAS,CAAC,CAAGnB,QAAQ,CAAC,GAAG,CAAC,CACzC,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACwB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAEhEF,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAAC4B,MAAM,CAACC,EAAE,CAAE,CACd,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAC/CF,MAAM,CAACG,GAAG,CAAG,oCAAoC,CACjDH,MAAM,CAACI,KAAK,CAAG,IAAI,CACnBH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC,CAEjCF,MAAM,CAACS,uBAAuB,CAAG,IAAM,CACrCC,gBAAgB,CAAC,CAAC,CACpB,CAAC,CACH,CAAC,IAAM,CACLA,gBAAgB,CAAC,CAAC,CACpB,CAEA,MAAO,IAAM,CACX,GAAIzB,iBAAiB,CAAC0B,OAAO,CAAE,CAC7B,GAAI,CACF1B,iBAAiB,CAAC0B,OAAO,CAACC,OAAO,CAAC,CAAC,CACrC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEF,KAAK,CAAC,CAChD,CACF,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAENzC,SAAS,CAAC,IAAM,CACd,GAAIa,iBAAiB,CAAC0B,OAAO,EAAI7B,OAAO,CAAE,CACxCkC,SAAS,CAAClC,OAAO,CAAC,CACpB,CACF,CAAC,CAAE,CAACA,OAAO,CAAC,CAAC,CAEbV,SAAS,CAAC,IAAM,CACd,GAAIa,iBAAiB,CAAC0B,OAAO,EAAI5B,QAAQ,CAAE,CACzCkC,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,CAAClC,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAA2B,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI1B,SAAS,CAAC2B,OAAO,EAAIX,MAAM,CAACC,EAAE,EAAID,MAAM,CAACC,EAAE,CAACiB,MAAM,CAAE,CACtDjC,iBAAiB,CAAC0B,OAAO,CAAG,GAAI,CAAAX,MAAM,CAACC,EAAE,CAACiB,MAAM,CAAClC,SAAS,CAAC2B,OAAO,CAAE,CAClEQ,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,MAAM,CACbtC,OAAO,CAAEA,OAAO,EAAI,EAAE,CACtBuC,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAExC,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEyC,YAAY,CAAG,CAAC,CAAG,CAAC,CACxCC,SAAS,CAAE1C,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEyC,YAAY,CAAG,CAAC,CAAG,CAAC,CACzCE,EAAE,CAAE,CAAC,CACLC,cAAc,CAAE,CAAC,CACjBC,cAAc,CAAE,CAAC,CACjBC,WAAW,CAAE,CAAC,CACdC,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,CAAC,CACjBC,EAAE,CAAE,IAAI,CACRC,YAAY,CAAE,IAAI,CAClBC,MAAM,CAAEnC,MAAM,CAACoC,QAAQ,CAACD,MAC1B,CAAC,CACDE,MAAM,CAAE,CACNC,OAAO,CAAEC,aAAa,CACtBC,aAAa,CAAEC,mBAAmB,CAClCC,OAAO,CAAEC,aACX,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAJ,aAAa,CAAIK,KAAK,EAAK,CAC/B9B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAEnC;AACA,KAAM,CAAA8B,SAAS,CAAGD,KAAK,CAACE,MAAM,CAACC,yBAAyB,CAAC,CAAC,CAC1DhD,qBAAqB,CAAC8C,SAAS,CAAC,CAEhC;AACA5B,aAAa,CAAC,CAAC,CAEf;AACA+B,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAED,KAAM,CAAAP,mBAAmB,CAAIG,KAAK,EAAK,CACrC,KAAM,CAAAK,MAAM,CAAG,CACb,IAAI,CAAE,WAAW,CACjB,GAAG,CAAE,OAAO,CACZ,GAAG,CAAE,SAAS,CACd,GAAG,CAAE,QAAQ,CACb,GAAG,CAAE,WAAW,CAChB,GAAG,CAAE,MACP,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGD,MAAM,CAACL,KAAK,CAACO,IAAI,CAAC,EAAI,SAAS,CAChDhE,cAAc,CAAC+D,QAAQ,CAAC,CAExB,GAAIA,QAAQ,GAAK,SAAS,CAAE,CAC1B3D,WAAW,CAACqD,KAAK,CAACE,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC,CACzC,CACF,CAAC,CAED,KAAM,CAAAT,aAAa,CAAIC,KAAK,EAAK,CAC/B9B,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAE+B,KAAK,CAACO,IAAI,CAAC,CAClD,KAAM,CAAAE,aAAa,CAAG,CACpB,CAAC,CAAE,uBAAuB,CAC1B,CAAC,CAAE,sBAAsB,CACzB,GAAG,CAAE,4BAA4B,CACjC,GAAG,CAAE,4BAA4B,CACjC,GAAG,CAAE,4BACP,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGD,aAAa,CAACT,KAAK,CAACO,IAAI,CAAC,EAAI,gCAAgC,CAClFI,KAAK,CAACD,YAAY,CAAC,CACrB,CAAC,CAED,KAAM,CAAAtC,SAAS,CAAG,KAAO,CAAAlC,OAAO,EAAK,CACnC,GAAIG,iBAAiB,CAAC0B,OAAO,CAAE,CAC7B,GAAI,CACF1B,iBAAiB,CAAC0B,OAAO,CAAC6C,aAAa,CAAC1E,OAAO,CAAC,CAEhD;AACA,KAAM,CAAA2E,IAAI,CAAG,KAAM,CAAAlF,aAAa,CAACmF,YAAY,CAAC5E,OAAO,CAAC,CACtDe,YAAY,CAAC4D,IAAI,CAAC,CACpB,CAAE,MAAO5C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CACF,CAAC,CAED,KAAM,CAAAI,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CAAChC,iBAAiB,CAAC0B,OAAO,CAAE,OAEhC,GAAI,CACF;AACA,GAAI5B,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAE4E,OAAO,EAAI5E,QAAQ,CAAC4E,OAAO,GAAK,MAAM,CAAE,CACpD1E,iBAAiB,CAAC0B,OAAO,CAACiD,kBAAkB,CAAC7E,QAAQ,CAAC4E,OAAO,CAAC,CAChE,CAEA;AACA,GAAI,CAAA5E,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAES,MAAM,IAAKqE,SAAS,CAAE,CAClC5E,iBAAiB,CAAC0B,OAAO,CAAClB,SAAS,CAACV,QAAQ,CAACS,MAAM,CAAC,CACpDC,SAAS,CAACV,QAAQ,CAACS,MAAM,CAAC,CAC5B,CACF,CAAE,MAAOqB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CAAC,CAED,KAAM,CAAAmC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAc,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAI7E,iBAAiB,CAAC0B,OAAO,EAAIzB,WAAW,GAAK,SAAS,CAAE,CAC1D,GAAI,CACF,KAAM,CAAA6E,IAAI,CAAG9E,iBAAiB,CAAC0B,OAAO,CAACqD,cAAc,CAAC,CAAC,CACvD3E,cAAc,CAAC0E,IAAI,CAAC,CACtB,CAAE,MAAOlD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CACF,CAAC,CAED,KAAM,CAAAoD,QAAQ,CAAGC,WAAW,CAACJ,UAAU,CAAE,IAAI,CAAC,CAC9C,MAAO,IAAMK,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAED;AACA,KAAM,CAAAG,SAAS,CAAGA,CAAA,GAAM,CACtB,GAAI,CAACnF,iBAAiB,CAAC0B,OAAO,CAAE,OAEhC,GAAIzB,WAAW,GAAK,SAAS,CAAE,CAC7BD,iBAAiB,CAAC0B,OAAO,CAAC0D,UAAU,CAAC,CAAC,CACxC,CAAC,IAAM,CACLpF,iBAAiB,CAAC0B,OAAO,CAAC2D,SAAS,CAAC,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAC,MAAM,CAAIR,IAAI,EAAK,CACvB,GAAI9E,iBAAiB,CAAC0B,OAAO,CAAE,CAC7B1B,iBAAiB,CAAC0B,OAAO,CAAC4D,MAAM,CAACR,IAAI,CAAE,IAAI,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAS,YAAY,CAAIC,SAAS,EAAK,CAClC,GAAIxF,iBAAiB,CAAC0B,OAAO,CAAE,CAC7B1B,iBAAiB,CAAC0B,OAAO,CAAClB,SAAS,CAACgF,SAAS,CAAC,CAC9ChF,SAAS,CAACgF,SAAS,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI,CAACvE,QAAQ,CAACwE,iBAAiB,CAAE,KAAAC,kBAAA,CAAAC,qBAAA,CAC/B,CAAAD,kBAAA,CAAA5F,SAAS,CAAC2B,OAAO,UAAAiE,kBAAA,kBAAAC,qBAAA,CAAjBD,kBAAA,CAAmBE,aAAa,UAAAD,qBAAA,iBAAhCA,qBAAA,CAAkCE,iBAAiB,CAAC,CAAC,CACrDpF,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,IAAM,CACLQ,QAAQ,CAAC6E,cAAc,CAAC,CAAC,CACzBrF,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAsF,UAAU,CAAIC,OAAO,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,KAAM,CAAAI,IAAI,CAAGF,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,MAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACtD,CAAC,CAED,GAAI,CAAC1G,OAAO,CAAE,CACZ,mBACEL,IAAA,QAAKgH,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC/G,KAAA,QAAK8G,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjH,IAAA,OAAAiH,QAAA,CAAI,8HAAwB,CAAI,CAAC,cACjCjH,IAAA,MAAAiH,QAAA,CAAG,oPAA+C,CAAG,CAAC,cACtDjH,IAAA,QAAKkH,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,QAAS,CAAE,CAAAH,QAAA,CAAC,cAAE,CAAK,CAAC,cAC5DjH,IAAA,MAAAiH,QAAA,CAAG,oSAAuD,CAAG,CAAC,cAE9D/G,KAAA,QAAKgH,KAAK,CAAE,CAAEG,SAAS,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAL,QAAA,eACrDjH,IAAA,OAAAiH,QAAA,CAAI,mDAAS,CAAI,CAAC,cAClB/G,KAAA,OAAIgH,KAAK,CAAE,CAAEK,SAAS,CAAE,MAAM,CAAEC,OAAO,CAAE,CAAC,CAAEH,SAAS,CAAE,MAAO,CAAE,CAAAJ,QAAA,eAC9DjH,IAAA,OAAAiH,QAAA,CAAI,qGAAmB,CAAI,CAAC,cAC5BjH,IAAA,OAAAiH,QAAA,CAAI,qIAA0B,CAAI,CAAC,cACnCjH,IAAA,OAAAiH,QAAA,CAAI,iJAA4B,CAAI,CAAC,cACrCjH,IAAA,OAAAiH,QAAA,CAAI,4GAAqB,CAAI,CAAC,cAC9BjH,IAAA,OAAAiH,QAAA,CAAI,kFAAe,CAAI,CAAC,EACtB,CAAC,EACF,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACEjH,IAAA,QAAKgH,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC/G,KAAA,QAAK8G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjH,IAAA,QACEyH,GAAG,CAAElH,SAAU,CACfyG,SAAS,CAAC,gBAAgB,CAC1BU,EAAE,CAAC,gBAAgB,CACpB,CAAC,CAGD,EAACpH,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEyC,YAAY,gBACtB/C,IAAA,QAAKgH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B/G,KAAA,QAAK8G,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BjH,IAAA,WACEgH,SAAS,CAAC,aAAa,CACvBW,OAAO,CAAEhC,SAAU,CACnBiC,KAAK,CAAEnH,WAAW,GAAK,SAAS,CAAG,YAAY,CAAG,OAAQ,CAAAwG,QAAA,CAEzDxG,WAAW,GAAK,SAAS,CAAG,IAAI,CAAG,IAAI,CAClC,CAAC,cAETP,KAAA,QAAK8G,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BT,UAAU,CAAC7F,WAAW,CAAC,CAAC,KAAG,CAAC6F,UAAU,CAAC3F,QAAQ,CAAC,EAC9C,CAAC,cAENb,IAAA,QAAKgH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCjH,IAAA,UACE6H,IAAI,CAAC,OAAO,CACZC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAElH,QAAS,CACdmH,KAAK,CAAErH,WAAY,CACnBsH,QAAQ,CAAGC,CAAC,EAAKpC,MAAM,CAACqC,UAAU,CAACD,CAAC,CAAC7D,MAAM,CAAC2D,KAAK,CAAC,CAAE,CACpDhB,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cAEN9G,KAAA,QAAK8G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjH,IAAA,SAAAiH,QAAA,CAAM,cAAE,CAAM,CAAC,cACfjH,IAAA,UACE6H,IAAI,CAAC,OAAO,CACZC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACTC,KAAK,CAAEjH,MAAO,CACdkH,QAAQ,CAAGC,CAAC,EAAKnC,YAAY,CAACqC,QAAQ,CAACF,CAAC,CAAC7D,MAAM,CAAC2D,KAAK,CAAC,CAAE,CACxDhB,SAAS,CAAC,YAAY,CACvB,CAAC,EACC,CAAC,cAENhH,IAAA,WACEgH,SAAS,CAAC,aAAa,CACvBW,OAAO,CAAE1B,gBAAiB,CAC1B2B,KAAK,CAAC,yDAAY,CAAAX,QAAA,CAEjBhG,YAAY,CAAG,IAAI,CAAG,IAAI,CACrB,CAAC,EACN,CAAC,CACH,CACN,CAGAE,SAAS,eACRjB,KAAA,QAAK8G,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjH,IAAA,OAAAiH,QAAA,CAAK9F,SAAS,CAACyG,KAAK,CAAK,CAAC,cAC1B5H,IAAA,MAAAiH,QAAA,CAAI9F,SAAS,CAACkH,OAAO,CAAI,CAAC,EACvB,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}