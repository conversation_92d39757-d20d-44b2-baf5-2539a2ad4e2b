{"ast": null, "code": "import React,{useState,useEffect}from'react';import{adBlocker}from'../utils/adBlocker';import{developerMonetization,integratedAds}from'../utils/developerMonetization';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DeveloperPanel=_ref=>{let{onClose}=_ref;const[adStats,setAdStats]=useState(adBlocker.getStats());const[analytics,setAnalytics]=useState(developerMonetization.getAnalytics());const[activeTab,setActiveTab]=useState('overview');const[newAd,setNewAd]=useState({title:'',description:'',link:'',type:'banner'});const[newAffiliate,setNewAffiliate]=useState({name:'',url:'',category:'',commission:5});useEffect(()=>{const interval=setInterval(()=>{setAdStats(adBlocker.getStats());setAnalytics(developerMonetization.getAnalytics());},2000);return()=>clearInterval(interval);},[]);const handleAddSponsoredContent=()=>{if(newAd.title&&newAd.description&&newAd.link){developerMonetization.addSponsoredContent({...newAd,image:'/assets/default-ad.jpg'// صورة افتراضية\n});setNewAd({title:'',description:'',link:'',type:'banner'});alert('تم إضافة المحتوى المدعوم بنجاح!');}};const handleAddAffiliateLink=()=>{if(newAffiliate.name&&newAffiliate.url){developerMonetization.addAffiliateLink(newAffiliate);setNewAffiliate({name:'',url:'',category:'',commission:5});alert('تم إضافة الرابط التابع بنجاح!');}};const formatNumber=num=>{return new Intl.NumberFormat('ar-SA').format(num);};return/*#__PURE__*/_jsx(\"div\",{className:\"developer-overlay\",onClick:onClose,children:/*#__PURE__*/_jsxs(\"div\",{className:\"developer-panel\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"panel-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDCBC \\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:onClose,children:\"\\u2715\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"panel-tabs\",children:[/*#__PURE__*/_jsx(\"button\",{className:`tab-btn ${activeTab==='overview'?'active':''}`,onClick:()=>setActiveTab('overview'),children:\"\\uD83D\\uDCCA \\u0646\\u0638\\u0631\\u0629 \\u0639\\u0627\\u0645\\u0629\"}),/*#__PURE__*/_jsx(\"button\",{className:`tab-btn ${activeTab==='ads'?'active':''}`,onClick:()=>setActiveTab('ads'),children:\"\\uD83D\\uDCE2 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"button\",{className:`tab-btn ${activeTab==='affiliates'?'active':''}`,onClick:()=>setActiveTab('affiliates'),children:\"\\uD83D\\uDD17 \\u0627\\u0644\\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u062A\\u0627\\u0628\\u0639\\u0629\"}),/*#__PURE__*/_jsx(\"button\",{className:`tab-btn ${activeTab==='settings'?'active':''}`,onClick:()=>setActiveTab('settings'),children:\"\\u2699\\uFE0F \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"})]}),activeTab==='overview'&&/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"revenue-overview\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"revenue-card main\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"revenue-icon\",children:\"\\uD83D\\uDCB0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"revenue-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"revenue-value\",children:analytics.revenueFormatted}),/*#__PURE__*/_jsx(\"div\",{className:\"revenue-label\",children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0623\\u0631\\u0628\\u0627\\u062D\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stats-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDC41\\uFE0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatNumber(analytics.impressions)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0645\\u0631\\u0627\\u062A \\u0627\\u0644\\u0638\\u0647\\u0648\\u0631\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDC46\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatNumber(analytics.clicks)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0627\\u0644\\u0646\\u0642\\u0631\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83C\\uDFAF\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:analytics.ctr}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u0646\\u0642\\u0631\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDC8E\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatNumber(analytics.conversions)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0627\\u0644\\u062A\\u062D\\u0648\\u064A\\u0644\\u0627\\u062A\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDCC8\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:analytics.conversionRate}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u062A\\u062D\\u0648\\u064A\\u0644\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-icon\",children:\"\\uD83D\\uDEE1\\uFE0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:formatNumber(adStats.totalBlocked)}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0645\\u062D\\u062C\\u0648\\u0628\\u0629\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"monetization-tips\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83D\\uDCA1 \\u0646\\u0635\\u0627\\u0626\\u062D \\u0644\\u0632\\u064A\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0623\\u0631\\u0628\\u0627\\u062D:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u0623\\u0636\\u0641 \\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u062C\\u0630\\u0627\\u0628\\u0629 \\u0648\\u0645\\u062A\\u0639\\u0644\\u0642\\u0629 \\u0628\\u0627\\u0644\\u0645\\u062D\\u062A\\u0648\\u0649\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u0627\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0631\\u0648\\u0627\\u0628\\u0637 \\u062A\\u0627\\u0628\\u0639\\u0629 \\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0639\\u0627\\u0644\\u064A\\u0629 \\u0627\\u0644\\u062C\\u0648\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u0631\\u0627\\u0642\\u0628 \\u0645\\u0639\\u062F\\u0644\\u0627\\u062A \\u0627\\u0644\\u0646\\u0642\\u0631 \\u0648\\u062D\\u0633\\u0646 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u0648\\u0632\\u0639 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0628\\u0634\\u0643\\u0644 \\u0645\\u062A\\u0648\\u0627\\u0632\\u0646 \\u0644\\u062A\\u062C\\u0646\\u0628 \\u0625\\u0632\\u0639\\u0627\\u062C \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u0627\\u062E\\u062A\\u0628\\u0631 \\u0623\\u0646\\u0648\\u0627\\u0639 \\u0645\\u062E\\u062A\\u0644\\u0641\\u0629 \\u0645\\u0646 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\"})]})]})]}),activeTab==='ads'&&/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"add-ad-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u062D\\u062A\\u0648\\u0649 \\u0645\\u062F\\u0639\\u0648\\u0645 \\u062C\\u062F\\u064A\\u062F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newAd.title,onChange:e=>setNewAd({...newAd,title:e.target.value}),placeholder:\"\\u0645\\u062B\\u0627\\u0644: \\u0627\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 \\u0623\\u0641\\u0636\\u0644 VPN\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646:\"}),/*#__PURE__*/_jsx(\"textarea\",{value:newAd.description,onChange:e=>setNewAd({...newAd,description:e.target.value}),placeholder:\"\\u0648\\u0635\\u0641 \\u0645\\u062E\\u062A\\u0635\\u0631 \\u0648\\u062C\\u0630\\u0627\\u0628 \\u0644\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\",rows:\"3\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:newAd.link,onChange:e=>setNewAd({...newAd,link:e.target.value}),placeholder:\"https://example.com?ref=youtube-player\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646:\"}),/*#__PURE__*/_jsxs(\"select\",{value:newAd.type,onChange:e=>setNewAd({...newAd,type:e.target.value}),children:[/*#__PURE__*/_jsx(\"option\",{value:\"banner\",children:\"\\u0628\\u0627\\u0646\\u0631 \\u0639\\u0644\\u0648\\u064A\"}),/*#__PURE__*/_jsx(\"option\",{value:\"sidebar\",children:\"\\u0625\\u0639\\u0644\\u0627\\u0646 \\u062C\\u0627\\u0646\\u0628\\u064A\"})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"add-btn\",onClick:handleAddSponsoredContent,children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ad-preview\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0645\\u0639\\u0627\\u064A\\u0646\\u0629 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646:\"}),newAd.title&&/*#__PURE__*/_jsx(\"div\",{className:\"preview-ad\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"ad-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"ad-text\",children:[/*#__PURE__*/_jsx(\"h5\",{children:newAd.title}),/*#__PURE__*/_jsx(\"p\",{children:newAd.description})]}),/*#__PURE__*/_jsx(\"div\",{className:\"ad-label\",children:\"\\u0625\\u0639\\u0644\\u0627\\u0646\"})]})})]})]}),activeTab==='affiliates'&&/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"add-affiliate-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0631\\u0627\\u0628\\u0637 \\u062A\\u0627\\u0628\\u0639 \\u062C\\u062F\\u064A\\u062F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u064A\\u0643:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newAffiliate.name,onChange:e=>setNewAffiliate({...newAffiliate,name:e.target.value}),placeholder:\"\\u0645\\u062B\\u0627\\u0644: \\u0645\\u062A\\u062C\\u0631 \\u0627\\u0644\\u062A\\u0642\\u0646\\u064A\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0625\\u062D\\u0627\\u0644\\u0629:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:newAffiliate.url,onChange:e=>setNewAffiliate({...newAffiliate,url:e.target.value}),placeholder:\"https://example.com?ref=your-id\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0627\\u0644\\u0641\\u0626\\u0629:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newAffiliate.category,onChange:e=>setNewAffiliate({...newAffiliate,category:e.target.value}),placeholder:\"\\u0645\\u062B\\u0627\\u0644: \\u062A\\u0642\\u0646\\u064A\\u0629\\u060C \\u0628\\u0631\\u0645\\u062C\\u064A\\u0627\\u062A\\u060C \\u062A\\u0639\\u0644\\u064A\\u0645\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u0646\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0648\\u0644\\u0629 (%):\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:newAffiliate.commission,onChange:e=>setNewAffiliate({...newAffiliate,commission:parseInt(e.target.value)}),min:\"1\",max:\"50\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"add-btn\",onClick:handleAddAffiliateLink,children:\"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u062A\\u0627\\u0628\\u0639\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"affiliate-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0643\\u064A\\u0641 \\u062A\\u0639\\u0645\\u0644 \\u0627\\u0644\\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u062A\\u0627\\u0628\\u0639\\u0629\\u061F\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u064A\\u062A\\u0645 \\u0639\\u0631\\u0636 \\u0627\\u0644\\u0631\\u0648\\u0627\\u0628\\u0637 \\u0628\\u0634\\u0643\\u0644 \\u0637\\u0628\\u064A\\u0639\\u064A \\u0641\\u064A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u0639\\u0646\\u062F \\u0646\\u0642\\u0631 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u060C \\u064A\\u062A\\u0645 \\u062A\\u0648\\u062C\\u064A\\u0647\\u0647 \\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0634\\u0631\\u064A\\u0643\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u062A\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 \\u0639\\u0645\\u0648\\u0644\\u0629 \\u0639\\u0646\\u062F \\u0625\\u062A\\u0645\\u0627\\u0645 \\u0639\\u0645\\u0644\\u064A\\u0629 \\u0634\\u0631\\u0627\\u0621\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u0644\\u0627 \\u064A\\u0624\\u062B\\u0631 \\u0639\\u0644\\u0649 \\u062A\\u062C\\u0631\\u0628\\u0629 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"})]})]})]}),activeTab==='settings'&&/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"monetization-settings\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0631\\u0628\\u062D\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0628\\u0627\\u0646\\u0631\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${developerMonetization.adConfig.bannerAds?'active':''}`,onClick:()=>{developerMonetization.updateAdConfig({bannerAds:!developerMonetization.adConfig.bannerAds});}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u062C\\u0627\\u0646\\u0628\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${developerMonetization.adConfig.sidebarAds?'active':''}`,onClick:()=>{developerMonetization.updateAdConfig({sidebarAds:!developerMonetization.adConfig.sidebarAds});}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0631\\u0648\\u0627\\u0628\\u0637 \\u0627\\u0644\\u062A\\u0627\\u0628\\u0639\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${developerMonetization.affiliateLinks.enabled?'active':''}`,onClick:()=>{developerMonetization.affiliateLinks.enabled=!developerMonetization.affiliateLinks.enabled;}})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"export-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"button\",{className:\"export-btn\",onClick:()=>{const data={analytics:analytics,adStats:adStats,timestamp:new Date().toISOString()};const blob=new Blob([JSON.stringify(data,null,2)],{type:'application/json'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=`youtube-player-analytics-${new Date().toISOString().split('T')[0]}.json`;a.click();},children:\"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\"})]})]})]})});};export default DeveloperPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ad<PERSON><PERSON><PERSON>", "developerMonetization", "integratedAds", "jsx", "_jsx", "jsxs", "_jsxs", "DeveloperPanel", "_ref", "onClose", "adStats", "setAdStats", "getStats", "analytics", "setAnalytics", "getAnalytics", "activeTab", "setActiveTab", "newAd", "setNewAd", "title", "description", "link", "type", "newAffiliate", "setNewAffiliate", "name", "url", "category", "commission", "interval", "setInterval", "clearInterval", "handleAddSponsoredContent", "addSponsored<PERSON>ontent", "image", "alert", "handleAddAffiliateLink", "addAffiliateLink", "formatNumber", "num", "Intl", "NumberFormat", "format", "className", "onClick", "children", "e", "stopPropagation", "revenueFormatted", "impressions", "clicks", "ctr", "conversions", "conversionRate", "totalBlocked", "value", "onChange", "target", "placeholder", "rows", "parseInt", "min", "max", "adConfig", "bannerAds", "updateAdConfig", "sidebarAds", "affiliateLinks", "enabled", "data", "timestamp", "Date", "toISOString", "blob", "Blob", "JSON", "stringify", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "split", "click"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/components/DeveloperPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { adBlocker } from '../utils/adBlocker';\nimport { developerMonetization, integratedAds } from '../utils/developerMonetization';\n\nconst DeveloperPanel = ({ onClose }) => {\n  const [adStats, setAdStats] = useState(adBlocker.getStats());\n  const [analytics, setAnalytics] = useState(developerMonetization.getAnalytics());\n  const [activeTab, setActiveTab] = useState('overview');\n  const [newAd, setNewAd] = useState({\n    title: '',\n    description: '',\n    link: '',\n    type: 'banner'\n  });\n  const [newAffiliate, setNewAffiliate] = useState({\n    name: '',\n    url: '',\n    category: '',\n    commission: 5\n  });\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAdStats(adBlocker.getStats());\n      setAnalytics(developerMonetization.getAnalytics());\n    }, 2000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleAddSponsoredContent = () => {\n    if (newAd.title && newAd.description && newAd.link) {\n      developerMonetization.addSponsoredContent({\n        ...newAd,\n        image: '/assets/default-ad.jpg' // صورة افتراضية\n      });\n      \n      setNewAd({ title: '', description: '', link: '', type: 'banner' });\n      alert('تم إضافة المحتوى المدعوم بنجاح!');\n    }\n  };\n\n  const handleAddAffiliateLink = () => {\n    if (newAffiliate.name && newAffiliate.url) {\n      developerMonetization.addAffiliateLink(newAffiliate);\n      setNewAffiliate({ name: '', url: '', category: '', commission: 5 });\n      alert('تم إضافة الرابط التابع بنجاح!');\n    }\n  };\n\n  const formatNumber = (num) => {\n    return new Intl.NumberFormat('ar-SA').format(num);\n  };\n\n  return (\n    <div className=\"developer-overlay\" onClick={onClose}>\n      <div className=\"developer-panel\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"panel-header\">\n          <h2>💼 لوحة تحكم المطور</h2>\n          <button className=\"close-btn\" onClick={onClose}>✕</button>\n        </div>\n\n        <div className=\"panel-tabs\">\n          <button \n            className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}\n            onClick={() => setActiveTab('overview')}\n          >\n            📊 نظرة عامة\n          </button>\n          <button \n            className={`tab-btn ${activeTab === 'ads' ? 'active' : ''}`}\n            onClick={() => setActiveTab('ads')}\n          >\n            📢 إدارة الإعلانات\n          </button>\n          <button \n            className={`tab-btn ${activeTab === 'affiliates' ? 'active' : ''}`}\n            onClick={() => setActiveTab('affiliates')}\n          >\n            🔗 الروابط التابعة\n          </button>\n          <button \n            className={`tab-btn ${activeTab === 'settings' ? 'active' : ''}`}\n            onClick={() => setActiveTab('settings')}\n          >\n            ⚙️ الإعدادات\n          </button>\n        </div>\n\n        {activeTab === 'overview' && (\n          <div className=\"tab-content\">\n            <div className=\"revenue-overview\">\n              <div className=\"revenue-card main\">\n                <div className=\"revenue-icon\">💰</div>\n                <div className=\"revenue-info\">\n                  <div className=\"revenue-value\">{analytics.revenueFormatted}</div>\n                  <div className=\"revenue-label\">إجمالي الأرباح</div>\n                </div>\n              </div>\n\n              <div className=\"stats-grid\">\n                <div className=\"stat-card\">\n                  <div className=\"stat-icon\">👁️</div>\n                  <div className=\"stat-info\">\n                    <div className=\"stat-value\">{formatNumber(analytics.impressions)}</div>\n                    <div className=\"stat-label\">مرات الظهور</div>\n                  </div>\n                </div>\n\n                <div className=\"stat-card\">\n                  <div className=\"stat-icon\">👆</div>\n                  <div className=\"stat-info\">\n                    <div className=\"stat-value\">{formatNumber(analytics.clicks)}</div>\n                    <div className=\"stat-label\">النقرات</div>\n                  </div>\n                </div>\n\n                <div className=\"stat-card\">\n                  <div className=\"stat-icon\">🎯</div>\n                  <div className=\"stat-info\">\n                    <div className=\"stat-value\">{analytics.ctr}</div>\n                    <div className=\"stat-label\">معدل النقر</div>\n                  </div>\n                </div>\n\n                <div className=\"stat-card\">\n                  <div className=\"stat-icon\">💎</div>\n                  <div className=\"stat-info\">\n                    <div className=\"stat-value\">{formatNumber(analytics.conversions)}</div>\n                    <div className=\"stat-label\">التحويلات</div>\n                  </div>\n                </div>\n\n                <div className=\"stat-card\">\n                  <div className=\"stat-icon\">📈</div>\n                  <div className=\"stat-info\">\n                    <div className=\"stat-value\">{analytics.conversionRate}</div>\n                    <div className=\"stat-label\">معدل التحويل</div>\n                  </div>\n                </div>\n\n                <div className=\"stat-card\">\n                  <div className=\"stat-icon\">🛡️</div>\n                  <div className=\"stat-info\">\n                    <div className=\"stat-value\">{formatNumber(adStats.totalBlocked)}</div>\n                    <div className=\"stat-label\">إعلانات محجوبة</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"monetization-tips\">\n              <h4>💡 نصائح لزيادة الأرباح:</h4>\n              <ul>\n                <li>أضف إعلانات جذابة ومتعلقة بالمحتوى</li>\n                <li>استخدم روابط تابعة لمنتجات عالية الجودة</li>\n                <li>راقب معدلات النقر وحسن الإعلانات</li>\n                <li>وزع الإعلانات بشكل متوازن لتجنب إزعاج المستخدمين</li>\n                <li>اختبر أنواع مختلفة من الإعلانات</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'ads' && (\n          <div className=\"tab-content\">\n            <div className=\"add-ad-section\">\n              <h4>إضافة محتوى مدعوم جديد</h4>\n              <div className=\"form-group\">\n                <label>عنوان الإعلان:</label>\n                <input\n                  type=\"text\"\n                  value={newAd.title}\n                  onChange={(e) => setNewAd({...newAd, title: e.target.value})}\n                  placeholder=\"مثال: احصل على أفضل VPN\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label>وصف الإعلان:</label>\n                <textarea\n                  value={newAd.description}\n                  onChange={(e) => setNewAd({...newAd, description: e.target.value})}\n                  placeholder=\"وصف مختصر وجذاب للإعلان\"\n                  rows=\"3\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label>رابط الإعلان:</label>\n                <input\n                  type=\"url\"\n                  value={newAd.link}\n                  onChange={(e) => setNewAd({...newAd, link: e.target.value})}\n                  placeholder=\"https://example.com?ref=youtube-player\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label>نوع الإعلان:</label>\n                <select\n                  value={newAd.type}\n                  onChange={(e) => setNewAd({...newAd, type: e.target.value})}\n                >\n                  <option value=\"banner\">بانر علوي</option>\n                  <option value=\"sidebar\">إعلان جانبي</option>\n                </select>\n              </div>\n              \n              <button className=\"add-btn\" onClick={handleAddSponsoredContent}>\n                إضافة الإعلان\n              </button>\n            </div>\n\n            <div className=\"ad-preview\">\n              <h4>معاينة الإعلان:</h4>\n              {newAd.title && (\n                <div className=\"preview-ad\">\n                  <div className=\"ad-content\">\n                    <div className=\"ad-text\">\n                      <h5>{newAd.title}</h5>\n                      <p>{newAd.description}</p>\n                    </div>\n                    <div className=\"ad-label\">إعلان</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'affiliates' && (\n          <div className=\"tab-content\">\n            <div className=\"add-affiliate-section\">\n              <h4>إضافة رابط تابع جديد</h4>\n              <div className=\"form-group\">\n                <label>اسم الشريك:</label>\n                <input\n                  type=\"text\"\n                  value={newAffiliate.name}\n                  onChange={(e) => setNewAffiliate({...newAffiliate, name: e.target.value})}\n                  placeholder=\"مثال: متجر التقنية\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label>رابط الإحالة:</label>\n                <input\n                  type=\"url\"\n                  value={newAffiliate.url}\n                  onChange={(e) => setNewAffiliate({...newAffiliate, url: e.target.value})}\n                  placeholder=\"https://example.com?ref=your-id\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label>الفئة:</label>\n                <input\n                  type=\"text\"\n                  value={newAffiliate.category}\n                  onChange={(e) => setNewAffiliate({...newAffiliate, category: e.target.value})}\n                  placeholder=\"مثال: تقنية، برمجيات، تعليم\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label>نسبة العمولة (%):</label>\n                <input\n                  type=\"number\"\n                  value={newAffiliate.commission}\n                  onChange={(e) => setNewAffiliate({...newAffiliate, commission: parseInt(e.target.value)})}\n                  min=\"1\"\n                  max=\"50\"\n                />\n              </div>\n              \n              <button className=\"add-btn\" onClick={handleAddAffiliateLink}>\n                إضافة الرابط التابع\n              </button>\n            </div>\n\n            <div className=\"affiliate-info\">\n              <h4>كيف تعمل الروابط التابعة؟</h4>\n              <ul>\n                <li>يتم عرض الروابط بشكل طبيعي في التطبيق</li>\n                <li>عند نقر المستخدم، يتم توجيهه لموقع الشريك</li>\n                <li>تحصل على عمولة عند إتمام عملية شراء</li>\n                <li>لا يؤثر على تجربة المستخدم</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'settings' && (\n          <div className=\"tab-content\">\n            <div className=\"monetization-settings\">\n              <h4>إعدادات الربح</h4>\n              \n              <div className=\"setting-item\">\n                <span>تفعيل الإعلانات البانر</span>\n                <div \n                  className={`toggle-switch ${developerMonetization.adConfig.bannerAds ? 'active' : ''}`}\n                  onClick={() => {\n                    developerMonetization.updateAdConfig({\n                      bannerAds: !developerMonetization.adConfig.bannerAds\n                    });\n                  }}\n                />\n              </div>\n\n              <div className=\"setting-item\">\n                <span>تفعيل الإعلانات الجانبية</span>\n                <div \n                  className={`toggle-switch ${developerMonetization.adConfig.sidebarAds ? 'active' : ''}`}\n                  onClick={() => {\n                    developerMonetization.updateAdConfig({\n                      sidebarAds: !developerMonetization.adConfig.sidebarAds\n                    });\n                  }}\n                />\n              </div>\n\n              <div className=\"setting-item\">\n                <span>تفعيل الروابط التابعة</span>\n                <div \n                  className={`toggle-switch ${developerMonetization.affiliateLinks.enabled ? 'active' : ''}`}\n                  onClick={() => {\n                    developerMonetization.affiliateLinks.enabled = !developerMonetization.affiliateLinks.enabled;\n                  }}\n                />\n              </div>\n            </div>\n\n            <div className=\"export-section\">\n              <h4>تصدير البيانات</h4>\n              <button \n                className=\"export-btn\"\n                onClick={() => {\n                  const data = {\n                    analytics: analytics,\n                    adStats: adStats,\n                    timestamp: new Date().toISOString()\n                  };\n                  const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});\n                  const url = URL.createObjectURL(blob);\n                  const a = document.createElement('a');\n                  a.href = url;\n                  a.download = `youtube-player-analytics-${new Date().toISOString().split('T')[0]}.json`;\n                  a.click();\n                }}\n              >\n                تصدير الإحصائيات\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default DeveloperPanel;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,KAAQ,oBAAoB,CAC9C,OAASC,qBAAqB,CAAEC,aAAa,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtF,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CACjC,KAAM,CAACE,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAACE,SAAS,CAACY,QAAQ,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGhB,QAAQ,CAACG,qBAAqB,CAACc,YAAY,CAAC,CAAC,CAAC,CAChF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAC,UAAU,CAAC,CACtD,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,CACjCsB,KAAK,CAAE,EAAE,CACTC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,EAAE,CACRC,IAAI,CAAE,QACR,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,CAC/C4B,IAAI,CAAE,EAAE,CACRC,GAAG,CAAE,EAAE,CACPC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAE,CACd,CAAC,CAAC,CAEF9B,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+B,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCpB,UAAU,CAACX,SAAS,CAACY,QAAQ,CAAC,CAAC,CAAC,CAChCE,YAAY,CAACb,qBAAqB,CAACc,YAAY,CAAC,CAAC,CAAC,CACpD,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMiB,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,yBAAyB,CAAGA,CAAA,GAAM,CACtC,GAAIf,KAAK,CAACE,KAAK,EAAIF,KAAK,CAACG,WAAW,EAAIH,KAAK,CAACI,IAAI,CAAE,CAClDrB,qBAAqB,CAACiC,mBAAmB,CAAC,CACxC,GAAGhB,KAAK,CACRiB,KAAK,CAAE,wBAAyB;AAClC,CAAC,CAAC,CAEFhB,QAAQ,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,WAAW,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,QAAS,CAAC,CAAC,CAClEa,KAAK,CAAC,iCAAiC,CAAC,CAC1C,CACF,CAAC,CAED,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACnC,GAAIb,YAAY,CAACE,IAAI,EAAIF,YAAY,CAACG,GAAG,CAAE,CACzC1B,qBAAqB,CAACqC,gBAAgB,CAACd,YAAY,CAAC,CACpDC,eAAe,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEC,UAAU,CAAE,CAAE,CAAC,CAAC,CACnEO,KAAK,CAAC,+BAA+B,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIC,GAAG,EAAK,CAC5B,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACH,GAAG,CAAC,CACnD,CAAC,CAED,mBACEpC,IAAA,QAAKwC,SAAS,CAAC,mBAAmB,CAACC,OAAO,CAAEpC,OAAQ,CAAAqC,QAAA,cAClDxC,KAAA,QAAKsC,SAAS,CAAC,iBAAiB,CAACC,OAAO,CAAGE,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAAF,QAAA,eACnExC,KAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3B1C,IAAA,OAAA0C,QAAA,CAAI,qGAAmB,CAAI,CAAC,cAC5B1C,IAAA,WAAQwC,SAAS,CAAC,WAAW,CAACC,OAAO,CAAEpC,OAAQ,CAAAqC,QAAA,CAAC,QAAC,CAAQ,CAAC,EACvD,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,WACEwC,SAAS,CAAE,WAAW5B,SAAS,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CACjE6B,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,UAAU,CAAE,CAAA6B,QAAA,CACzC,gEAED,CAAQ,CAAC,cACT1C,IAAA,WACEwC,SAAS,CAAE,WAAW5B,SAAS,GAAK,KAAK,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC5D6B,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,KAAK,CAAE,CAAA6B,QAAA,CACpC,oGAED,CAAQ,CAAC,cACT1C,IAAA,WACEwC,SAAS,CAAE,WAAW5B,SAAS,GAAK,YAAY,CAAG,QAAQ,CAAG,EAAE,EAAG,CACnE6B,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,YAAY,CAAE,CAAA6B,QAAA,CAC3C,oGAED,CAAQ,CAAC,cACT1C,IAAA,WACEwC,SAAS,CAAE,WAAW5B,SAAS,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CACjE6B,OAAO,CAAEA,CAAA,GAAM5B,YAAY,CAAC,UAAU,CAAE,CAAA6B,QAAA,CACzC,qEAED,CAAQ,CAAC,EACN,CAAC,CAEL9B,SAAS,GAAK,UAAU,eACvBV,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1BxC,KAAA,QAAKsC,SAAS,CAAC,kBAAkB,CAAAE,QAAA,eAC/BxC,KAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC1C,IAAA,QAAKwC,SAAS,CAAC,cAAc,CAAAE,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCxC,KAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3B1C,IAAA,QAAKwC,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAEjC,SAAS,CAACoC,gBAAgB,CAAM,CAAC,cACjE7C,IAAA,QAAKwC,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAC,iFAAc,CAAK,CAAC,EAChD,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzBxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,oBAAG,CAAK,CAAC,cACpCxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAEP,YAAY,CAAC1B,SAAS,CAACqC,WAAW,CAAC,CAAM,CAAC,cACvE9C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAC,+DAAW,CAAK,CAAC,EAC1C,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,cAAE,CAAK,CAAC,cACnCxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAEP,YAAY,CAAC1B,SAAS,CAACsC,MAAM,CAAC,CAAM,CAAC,cAClE/C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAC,4CAAO,CAAK,CAAC,EACtC,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,cAAE,CAAK,CAAC,cACnCxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAEjC,SAAS,CAACuC,GAAG,CAAM,CAAC,cACjDhD,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAC,yDAAU,CAAK,CAAC,EACzC,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,cAAE,CAAK,CAAC,cACnCxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAEP,YAAY,CAAC1B,SAAS,CAACwC,WAAW,CAAC,CAAM,CAAC,cACvEjD,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAC,wDAAS,CAAK,CAAC,EACxC,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,cAAE,CAAK,CAAC,cACnCxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAEjC,SAAS,CAACyC,cAAc,CAAM,CAAC,cAC5DlD,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAC,qEAAY,CAAK,CAAC,EAC3C,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,oBAAG,CAAK,CAAC,cACpCxC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB1C,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAEP,YAAY,CAAC7B,OAAO,CAAC6C,YAAY,CAAC,CAAM,CAAC,cACtEnD,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,CAAC,iFAAc,CAAK,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC1C,IAAA,OAAA0C,QAAA,CAAI,8HAAwB,CAAI,CAAC,cACjCxC,KAAA,OAAAwC,QAAA,eACE1C,IAAA,OAAA0C,QAAA,CAAI,0LAAkC,CAAI,CAAC,cAC3C1C,IAAA,OAAA0C,QAAA,CAAI,mNAAuC,CAAI,CAAC,cAChD1C,IAAA,OAAA0C,QAAA,CAAI,8KAAgC,CAAI,CAAC,cACzC1C,IAAA,OAAA0C,QAAA,CAAI,oQAAgD,CAAI,CAAC,cACzD1C,IAAA,OAAA0C,QAAA,CAAI,wKAA+B,CAAI,CAAC,EACtC,CAAC,EACF,CAAC,EACH,CACN,CAEA9B,SAAS,GAAK,KAAK,eAClBV,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1BxC,KAAA,QAAKsC,SAAS,CAAC,gBAAgB,CAAAE,QAAA,eAC7B1C,IAAA,OAAA0C,QAAA,CAAI,uHAAsB,CAAI,CAAC,cAC/BxC,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,4EAAc,CAAO,CAAC,cAC7B1C,IAAA,UACEmB,IAAI,CAAC,MAAM,CACXiC,KAAK,CAAEtC,KAAK,CAACE,KAAM,CACnBqC,QAAQ,CAAGV,CAAC,EAAK5B,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAEE,KAAK,CAAE2B,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CAC7DG,WAAW,CAAC,oGAAyB,CACtC,CAAC,EACC,CAAC,cAENrD,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,gEAAY,CAAO,CAAC,cAC3B1C,IAAA,aACEoD,KAAK,CAAEtC,KAAK,CAACG,WAAY,CACzBoC,QAAQ,CAAGV,CAAC,EAAK5B,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAEG,WAAW,CAAE0B,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CACnEG,WAAW,CAAC,6HAAyB,CACrCC,IAAI,CAAC,GAAG,CACT,CAAC,EACC,CAAC,cAENtD,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,sEAAa,CAAO,CAAC,cAC5B1C,IAAA,UACEmB,IAAI,CAAC,KAAK,CACViC,KAAK,CAAEtC,KAAK,CAACI,IAAK,CAClBmC,QAAQ,CAAGV,CAAC,EAAK5B,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAEI,IAAI,CAAEyB,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CAC5DG,WAAW,CAAC,wCAAwC,CACrD,CAAC,EACC,CAAC,cAENrD,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,gEAAY,CAAO,CAAC,cAC3BxC,KAAA,WACEkD,KAAK,CAAEtC,KAAK,CAACK,IAAK,CAClBkC,QAAQ,CAAGV,CAAC,EAAK5B,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAEK,IAAI,CAAEwB,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CAAAV,QAAA,eAE5D1C,IAAA,WAAQoD,KAAK,CAAC,QAAQ,CAAAV,QAAA,CAAC,mDAAS,CAAQ,CAAC,cACzC1C,IAAA,WAAQoD,KAAK,CAAC,SAAS,CAAAV,QAAA,CAAC,+DAAW,CAAQ,CAAC,EACtC,CAAC,EACN,CAAC,cAEN1C,IAAA,WAAQwC,SAAS,CAAC,SAAS,CAACC,OAAO,CAAEZ,yBAA0B,CAAAa,QAAA,CAAC,2EAEhE,CAAQ,CAAC,EACN,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,OAAA0C,QAAA,CAAI,kFAAe,CAAI,CAAC,CACvB5B,KAAK,CAACE,KAAK,eACVhB,IAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAE,QAAA,cACzBxC,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzBxC,KAAA,QAAKsC,SAAS,CAAC,SAAS,CAAAE,QAAA,eACtB1C,IAAA,OAAA0C,QAAA,CAAK5B,KAAK,CAACE,KAAK,CAAK,CAAC,cACtBhB,IAAA,MAAA0C,QAAA,CAAI5B,KAAK,CAACG,WAAW,CAAI,CAAC,EACvB,CAAC,cACNjB,IAAA,QAAKwC,SAAS,CAAC,UAAU,CAAAE,QAAA,CAAC,gCAAK,CAAK,CAAC,EAClC,CAAC,CACH,CACN,EACE,CAAC,EACH,CACN,CAEA9B,SAAS,GAAK,YAAY,eACzBV,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1BxC,KAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACpC1C,IAAA,OAAA0C,QAAA,CAAI,2GAAoB,CAAI,CAAC,cAC7BxC,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,0DAAW,CAAO,CAAC,cAC1B1C,IAAA,UACEmB,IAAI,CAAC,MAAM,CACXiC,KAAK,CAAEhC,YAAY,CAACE,IAAK,CACzB+B,QAAQ,CAAGV,CAAC,EAAKtB,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAEE,IAAI,CAAEqB,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CAC1EG,WAAW,CAAC,+FAAoB,CACjC,CAAC,EACC,CAAC,cAENrD,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,sEAAa,CAAO,CAAC,cAC5B1C,IAAA,UACEmB,IAAI,CAAC,KAAK,CACViC,KAAK,CAAEhC,YAAY,CAACG,GAAI,CACxB8B,QAAQ,CAAGV,CAAC,EAAKtB,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAEG,GAAG,CAAEoB,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CACzEG,WAAW,CAAC,iCAAiC,CAC9C,CAAC,EACC,CAAC,cAENrD,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,iCAAM,CAAO,CAAC,cACrB1C,IAAA,UACEmB,IAAI,CAAC,MAAM,CACXiC,KAAK,CAAEhC,YAAY,CAACI,QAAS,CAC7B6B,QAAQ,CAAGV,CAAC,EAAKtB,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAEI,QAAQ,CAAEmB,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAE,CAC9EG,WAAW,CAAC,gJAA6B,CAC1C,CAAC,EACC,CAAC,cAENrD,KAAA,QAAKsC,SAAS,CAAC,YAAY,CAAAE,QAAA,eACzB1C,IAAA,UAAA0C,QAAA,CAAO,0EAAiB,CAAO,CAAC,cAChC1C,IAAA,UACEmB,IAAI,CAAC,QAAQ,CACbiC,KAAK,CAAEhC,YAAY,CAACK,UAAW,CAC/B4B,QAAQ,CAAGV,CAAC,EAAKtB,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAEK,UAAU,CAAEgC,QAAQ,CAACd,CAAC,CAACW,MAAM,CAACF,KAAK,CAAC,CAAC,CAAE,CAC1FM,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,IAAI,CACT,CAAC,EACC,CAAC,cAEN3D,IAAA,WAAQwC,SAAS,CAAC,SAAS,CAACC,OAAO,CAAER,sBAAuB,CAAAS,QAAA,CAAC,0GAE7D,CAAQ,CAAC,EACN,CAAC,cAENxC,KAAA,QAAKsC,SAAS,CAAC,gBAAgB,CAAAE,QAAA,eAC7B1C,IAAA,OAAA0C,QAAA,CAAI,yIAAyB,CAAI,CAAC,cAClCxC,KAAA,OAAAwC,QAAA,eACE1C,IAAA,OAAA0C,QAAA,CAAI,kMAAqC,CAAI,CAAC,cAC9C1C,IAAA,OAAA0C,QAAA,CAAI,0NAAyC,CAAI,CAAC,cAClD1C,IAAA,OAAA0C,QAAA,CAAI,sLAAmC,CAAI,CAAC,cAC5C1C,IAAA,OAAA0C,QAAA,CAAI,0IAA0B,CAAI,CAAC,EACjC,CAAC,EACF,CAAC,EACH,CACN,CAEA9B,SAAS,GAAK,UAAU,eACvBV,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1BxC,KAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACpC1C,IAAA,OAAA0C,QAAA,CAAI,2EAAa,CAAI,CAAC,cAEtBxC,KAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3B1C,IAAA,SAAA0C,QAAA,CAAM,4HAAsB,CAAM,CAAC,cACnC1C,IAAA,QACEwC,SAAS,CAAE,iBAAiB3C,qBAAqB,CAAC+D,QAAQ,CAACC,SAAS,CAAG,QAAQ,CAAG,EAAE,EAAG,CACvFpB,OAAO,CAAEA,CAAA,GAAM,CACb5C,qBAAqB,CAACiE,cAAc,CAAC,CACnCD,SAAS,CAAE,CAAChE,qBAAqB,CAAC+D,QAAQ,CAACC,SAC7C,CAAC,CAAC,CACJ,CAAE,CACH,CAAC,EACC,CAAC,cAEN3D,KAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3B1C,IAAA,SAAA0C,QAAA,CAAM,wIAAwB,CAAM,CAAC,cACrC1C,IAAA,QACEwC,SAAS,CAAE,iBAAiB3C,qBAAqB,CAAC+D,QAAQ,CAACG,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CACxFtB,OAAO,CAAEA,CAAA,GAAM,CACb5C,qBAAqB,CAACiE,cAAc,CAAC,CACnCC,UAAU,CAAE,CAAClE,qBAAqB,CAAC+D,QAAQ,CAACG,UAC9C,CAAC,CAAC,CACJ,CAAE,CACH,CAAC,EACC,CAAC,cAEN7D,KAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3B1C,IAAA,SAAA0C,QAAA,CAAM,sHAAqB,CAAM,CAAC,cAClC1C,IAAA,QACEwC,SAAS,CAAE,iBAAiB3C,qBAAqB,CAACmE,cAAc,CAACC,OAAO,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC3FxB,OAAO,CAAEA,CAAA,GAAM,CACb5C,qBAAqB,CAACmE,cAAc,CAACC,OAAO,CAAG,CAACpE,qBAAqB,CAACmE,cAAc,CAACC,OAAO,CAC9F,CAAE,CACH,CAAC,EACC,CAAC,EACH,CAAC,cAEN/D,KAAA,QAAKsC,SAAS,CAAC,gBAAgB,CAAAE,QAAA,eAC7B1C,IAAA,OAAA0C,QAAA,CAAI,iFAAc,CAAI,CAAC,cACvB1C,IAAA,WACEwC,SAAS,CAAC,YAAY,CACtBC,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAyB,IAAI,CAAG,CACXzD,SAAS,CAAEA,SAAS,CACpBH,OAAO,CAAEA,OAAO,CAChB6D,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACD,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACP,IAAI,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAAE,CAAC/C,IAAI,CAAE,kBAAkB,CAAC,CAAC,CAClF,KAAM,CAAAI,GAAG,CAAGmD,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CACrC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGxD,GAAG,CACZqD,CAAC,CAACI,QAAQ,CAAG,4BAA4B,GAAI,CAAAZ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CACtFL,CAAC,CAACM,KAAK,CAAC,CAAC,CACX,CAAE,CAAAxC,QAAA,CACH,6FAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}