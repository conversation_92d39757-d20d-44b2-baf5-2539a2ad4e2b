{"ast": null, "code": "// نظام الربح للمطور\nexport class DeveloperMonetization{constructor(){this.isEnabled=true;this.adConfig={bannerAds:true,sidebarAds:true,videoOverlayAds:false,popupAds:false};this.affiliateLinks={enabled:true,links:[{id:'tech-store',name:'متجر التقنية',url:'https://example.com/tech?ref=youtube-player',category:'تقنية',commission:5},{id:'software-deals',name:'عروض البرمجيات',url:'https://example.com/software?ref=youtube-player',category:'برمجيات',commission:10}]};this.sponsoredContent={enabled:true,content:[{id:'sponsor-1',title:'احصل على أفضل VPN',description:'حماية كاملة لتصفحك مع خصم 50%',link:'https://example.com/vpn?ref=youtube-player',image:'/assets/sponsor-vpn.jpg',type:'banner'},{id:'sponsor-2',title:'كورسات البرمجة المجانية',description:'تعلم البرمجة من الصفر حتى الاحتراف',link:'https://example.com/courses?ref=youtube-player',image:'/assets/sponsor-courses.jpg',type:'sidebar'}]};this.analytics={impressions:0,clicks:0,conversions:0,revenue:0};this.loadAnalytics();}// تحميل الإحصائيات\nloadAnalytics(){const saved=localStorage.getItem('developer-analytics');if(saved){try{this.analytics={...this.analytics,...JSON.parse(saved)};}catch(error){console.error('Error loading analytics:',error);}}}// حفظ الإحصائيات\nsaveAnalytics(){localStorage.setItem('developer-analytics',JSON.stringify(this.analytics));}// تسجيل عرض إعلان\nrecordImpression(adId,adType){this.analytics.impressions++;this.saveAnalytics();// إرسال البيانات لخادم التحليلات (في التطبيق الحقيقي)\nthis.sendAnalytics('impression',{adId,adType});}// تسجيل نقرة على إعلان\nrecordClick(adId,adType){let revenue=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;this.analytics.clicks++;this.analytics.revenue+=revenue;this.saveAnalytics();// إرسال البيانات لخادم التحليلات\nthis.sendAnalytics('click',{adId,adType,revenue});}// تسجيل تحويل (شراء/اشتراك)\nrecordConversion(adId,revenue){this.analytics.conversions++;this.analytics.revenue+=revenue;this.saveAnalytics();this.sendAnalytics('conversion',{adId,revenue});}// إرسال البيانات للتحليلات (محاكاة)\nsendAnalytics(event,data){// في التطبيق الحقيقي، ستقوم بإرسال البيانات لخادم التحليلات\nconsole.log(`Analytics Event: ${event}`,data);// محاكاة إرسال البيانات\nif(navigator.onLine){// fetch('https://your-analytics-server.com/track', {\n//   method: 'POST',\n//   headers: { 'Content-Type': 'application/json' },\n//   body: JSON.stringify({ event, data, timestamp: new Date().toISOString() })\n// });\n}}// الحصول على إعلان عشوائي للعرض\ngetRandomAd(){let type=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'banner';const ads=this.sponsoredContent.content.filter(ad=>ad.type===type);if(ads.length===0)return null;const randomAd=ads[Math.floor(Math.random()*ads.length)];this.recordImpression(randomAd.id,type);return randomAd;}// الحصول على رابط تابع عشوائي\ngetRandomAffiliateLink(){let category=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;let links=this.affiliateLinks.links;if(category){links=links.filter(link=>link.category===category);}if(links.length===0)return null;return links[Math.floor(Math.random()*links.length)];}// إضافة محتوى مدعوم جديد\naddSponsoredContent(content){this.sponsoredContent.content.push({...content,id:`sponsor-${Date.now()}`});}// إضافة رابط تابع جديد\naddAffiliateLink(link){this.affiliateLinks.links.push({...link,id:`affiliate-${Date.now()}`});}// تحديث إعدادات الإعلانات\nupdateAdConfig(config){this.adConfig={...this.adConfig,...config};}// الحصول على الإحصائيات\ngetAnalytics(){const ctr=this.analytics.impressions>0?(this.analytics.clicks/this.analytics.impressions*100).toFixed(2):0;const conversionRate=this.analytics.clicks>0?(this.analytics.conversions/this.analytics.clicks*100).toFixed(2):0;return{...this.analytics,ctr:`${ctr}%`,conversionRate:`${conversionRate}%`,revenueFormatted:new Intl.NumberFormat('ar-SA',{style:'currency',currency:'USD'}).format(this.analytics.revenue)};}// تفعيل/إلغاء تفعيل نظام الربح\ntoggle(){this.isEnabled=!this.isEnabled;return this.isEnabled;}}// نظام الإعلانات المدمجة\nexport class IntegratedAds{constructor(monetization){this.monetization=monetization;this.adElements=new Map();this.rotationInterval=30000;// تغيير الإعلانات كل 30 ثانية\n}// إنشاء إعلان بانر\ncreateBannerAd(containerId){const ad=this.monetization.getRandomAd('banner');if(!ad||!this.monetization.adConfig.bannerAds)return null;const adElement=document.createElement('div');adElement.className='integrated-banner-ad';adElement.innerHTML=`\n      <div class=\"ad-content\" onclick=\"window.open('${ad.link}', '_blank')\">\n        <img src=\"${ad.image}\" alt=\"${ad.title}\" onerror=\"this.style.display='none'\">\n        <div class=\"ad-text\">\n          <h4>${ad.title}</h4>\n          <p>${ad.description}</p>\n        </div>\n        <div class=\"ad-label\">إعلان</div>\n      </div>\n    `;// تسجيل النقرة\nadElement.addEventListener('click',()=>{this.monetization.recordClick(ad.id,'banner',0.05);// 5 سنت لكل نقرة\n});this.adElements.set(containerId,adElement);return adElement;}// إنشاء إعلان جانبي\ncreateSidebarAd(){const ad=this.monetization.getRandomAd('sidebar');if(!ad||!this.monetization.adConfig.sidebarAds)return null;const adElement=document.createElement('div');adElement.className='integrated-sidebar-ad';adElement.innerHTML=`\n      <div class=\"ad-content\" onclick=\"window.open('${ad.link}', '_blank')\">\n        <img src=\"${ad.image}\" alt=\"${ad.title}\" onerror=\"this.style.display='none'\">\n        <div class=\"ad-text\">\n          <h5>${ad.title}</h5>\n          <p>${ad.description}</p>\n        </div>\n        <div class=\"ad-label\">إعلان</div>\n      </div>\n    `;adElement.addEventListener('click',()=>{this.monetization.recordClick(ad.id,'sidebar',0.03);});return adElement;}// إنشاء رابط تابع مدمج\ncreateAffiliateLink(text){let category=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;const link=this.monetization.getRandomAffiliateLink(category);if(!link||!this.monetization.affiliateLinks.enabled)return text;const linkElement=document.createElement('a');linkElement.href=link.url;linkElement.target='_blank';linkElement.textContent=text;linkElement.className='affiliate-link';linkElement.title=`${link.name} - عمولة ${link.commission}%`;linkElement.addEventListener('click',()=>{this.monetization.recordClick(link.id,'affiliate',0.10);});return linkElement.outerHTML;}// بدء دوران الإعلانات\nstartAdRotation(){setInterval(()=>{this.rotateAds();},this.rotationInterval);}// تدوير الإعلانات\nrotateAds(){this.adElements.forEach((element,containerId)=>{const container=document.getElementById(containerId);if(container&&element.parentNode===container){container.removeChild(element);const newAd=this.createBannerAd(containerId);if(newAd){container.appendChild(newAd);}}});}}// إنشاء مثيلات عامة\nexport const developerMonetization=new DeveloperMonetization();export const integratedAds=new IntegratedAds(developerMonetization);", "map": {"version": 3, "names": ["DeveloperMonetization", "constructor", "isEnabled", "adConfig", "bannerAds", "sidebarAds", "videoOverlayAds", "popupAds", "affiliateLinks", "enabled", "links", "id", "name", "url", "category", "commission", "sponsored<PERSON><PERSON>nt", "content", "title", "description", "link", "image", "type", "analytics", "impressions", "clicks", "conversions", "revenue", "loadAnalytics", "saved", "localStorage", "getItem", "JSON", "parse", "error", "console", "saveAnalytics", "setItem", "stringify", "recordImpression", "adId", "adType", "sendAnalytics", "recordClick", "arguments", "length", "undefined", "recordConversion", "event", "data", "log", "navigator", "onLine", "getRandomAd", "ads", "filter", "ad", "randomAd", "Math", "floor", "random", "getRandomAffiliateLink", "addSponsored<PERSON>ontent", "push", "Date", "now", "addAffiliateLink", "updateAdConfig", "config", "getAnalytics", "ctr", "toFixed", "conversionRate", "revenueFormatted", "Intl", "NumberFormat", "style", "currency", "format", "toggle", "IntegratedAds", "monetization", "adElements", "Map", "rotationInterval", "createBannerAd", "containerId", "adElement", "document", "createElement", "className", "innerHTML", "addEventListener", "set", "createSidebarAd", "createAffiliateLink", "text", "linkElement", "href", "target", "textContent", "outerHTML", "startAdRotation", "setInterval", "rotateAds", "for<PERSON>ach", "element", "container", "getElementById", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "newAd", "append<PERSON><PERSON><PERSON>", "developerMonetization", "integratedAds"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/utils/developerMonetization.js"], "sourcesContent": ["// نظام الربح للمطور\nexport class DeveloperMonetization {\n  constructor() {\n    this.isEnabled = true;\n    this.adConfig = {\n      bannerAds: true,\n      sidebarAds: true,\n      videoOverlayAds: false,\n      popupAds: false\n    };\n    \n    this.affiliateLinks = {\n      enabled: true,\n      links: [\n        {\n          id: 'tech-store',\n          name: 'متجر التقنية',\n          url: 'https://example.com/tech?ref=youtube-player',\n          category: 'تقنية',\n          commission: 5\n        },\n        {\n          id: 'software-deals',\n          name: 'عروض البرمجيات',\n          url: 'https://example.com/software?ref=youtube-player',\n          category: 'برمجيات',\n          commission: 10\n        }\n      ]\n    };\n\n    this.sponsoredContent = {\n      enabled: true,\n      content: [\n        {\n          id: 'sponsor-1',\n          title: 'احصل على أفضل VPN',\n          description: 'حماية كاملة لتصفحك مع خصم 50%',\n          link: 'https://example.com/vpn?ref=youtube-player',\n          image: '/assets/sponsor-vpn.jpg',\n          type: 'banner'\n        },\n        {\n          id: 'sponsor-2',\n          title: 'كورسات البرمجة المجانية',\n          description: 'تعلم البرمجة من الصفر حتى الاحتراف',\n          link: 'https://example.com/courses?ref=youtube-player',\n          image: '/assets/sponsor-courses.jpg',\n          type: 'sidebar'\n        }\n      ]\n    };\n\n    this.analytics = {\n      impressions: 0,\n      clicks: 0,\n      conversions: 0,\n      revenue: 0\n    };\n\n    this.loadAnalytics();\n  }\n\n  // تحميل الإحصائيات\n  loadAnalytics() {\n    const saved = localStorage.getItem('developer-analytics');\n    if (saved) {\n      try {\n        this.analytics = { ...this.analytics, ...JSON.parse(saved) };\n      } catch (error) {\n        console.error('Error loading analytics:', error);\n      }\n    }\n  }\n\n  // حفظ الإحصائيات\n  saveAnalytics() {\n    localStorage.setItem('developer-analytics', JSON.stringify(this.analytics));\n  }\n\n  // تسجيل عرض إعلان\n  recordImpression(adId, adType) {\n    this.analytics.impressions++;\n    this.saveAnalytics();\n    \n    // إرسال البيانات لخادم التحليلات (في التطبيق الحقيقي)\n    this.sendAnalytics('impression', { adId, adType });\n  }\n\n  // تسجيل نقرة على إعلان\n  recordClick(adId, adType, revenue = 0) {\n    this.analytics.clicks++;\n    this.analytics.revenue += revenue;\n    this.saveAnalytics();\n    \n    // إرسال البيانات لخادم التحليلات\n    this.sendAnalytics('click', { adId, adType, revenue });\n  }\n\n  // تسجيل تحويل (شراء/اشتراك)\n  recordConversion(adId, revenue) {\n    this.analytics.conversions++;\n    this.analytics.revenue += revenue;\n    this.saveAnalytics();\n    \n    this.sendAnalytics('conversion', { adId, revenue });\n  }\n\n  // إرسال البيانات للتحليلات (محاكاة)\n  sendAnalytics(event, data) {\n    // في التطبيق الحقيقي، ستقوم بإرسال البيانات لخادم التحليلات\n    console.log(`Analytics Event: ${event}`, data);\n    \n    // محاكاة إرسال البيانات\n    if (navigator.onLine) {\n      // fetch('https://your-analytics-server.com/track', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ event, data, timestamp: new Date().toISOString() })\n      // });\n    }\n  }\n\n  // الحصول على إعلان عشوائي للعرض\n  getRandomAd(type = 'banner') {\n    const ads = this.sponsoredContent.content.filter(ad => ad.type === type);\n    if (ads.length === 0) return null;\n    \n    const randomAd = ads[Math.floor(Math.random() * ads.length)];\n    this.recordImpression(randomAd.id, type);\n    \n    return randomAd;\n  }\n\n  // الحصول على رابط تابع عشوائي\n  getRandomAffiliateLink(category = null) {\n    let links = this.affiliateLinks.links;\n    \n    if (category) {\n      links = links.filter(link => link.category === category);\n    }\n    \n    if (links.length === 0) return null;\n    \n    return links[Math.floor(Math.random() * links.length)];\n  }\n\n  // إضافة محتوى مدعوم جديد\n  addSponsoredContent(content) {\n    this.sponsoredContent.content.push({\n      ...content,\n      id: `sponsor-${Date.now()}`\n    });\n  }\n\n  // إضافة رابط تابع جديد\n  addAffiliateLink(link) {\n    this.affiliateLinks.links.push({\n      ...link,\n      id: `affiliate-${Date.now()}`\n    });\n  }\n\n  // تحديث إعدادات الإعلانات\n  updateAdConfig(config) {\n    this.adConfig = { ...this.adConfig, ...config };\n  }\n\n  // الحصول على الإحصائيات\n  getAnalytics() {\n    const ctr = this.analytics.impressions > 0 \n      ? (this.analytics.clicks / this.analytics.impressions * 100).toFixed(2)\n      : 0;\n    \n    const conversionRate = this.analytics.clicks > 0\n      ? (this.analytics.conversions / this.analytics.clicks * 100).toFixed(2)\n      : 0;\n\n    return {\n      ...this.analytics,\n      ctr: `${ctr}%`,\n      conversionRate: `${conversionRate}%`,\n      revenueFormatted: new Intl.NumberFormat('ar-SA', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(this.analytics.revenue)\n    };\n  }\n\n  // تفعيل/إلغاء تفعيل نظام الربح\n  toggle() {\n    this.isEnabled = !this.isEnabled;\n    return this.isEnabled;\n  }\n}\n\n// نظام الإعلانات المدمجة\nexport class IntegratedAds {\n  constructor(monetization) {\n    this.monetization = monetization;\n    this.adElements = new Map();\n    this.rotationInterval = 30000; // تغيير الإعلانات كل 30 ثانية\n  }\n\n  // إنشاء إعلان بانر\n  createBannerAd(containerId) {\n    const ad = this.monetization.getRandomAd('banner');\n    if (!ad || !this.monetization.adConfig.bannerAds) return null;\n\n    const adElement = document.createElement('div');\n    adElement.className = 'integrated-banner-ad';\n    adElement.innerHTML = `\n      <div class=\"ad-content\" onclick=\"window.open('${ad.link}', '_blank')\">\n        <img src=\"${ad.image}\" alt=\"${ad.title}\" onerror=\"this.style.display='none'\">\n        <div class=\"ad-text\">\n          <h4>${ad.title}</h4>\n          <p>${ad.description}</p>\n        </div>\n        <div class=\"ad-label\">إعلان</div>\n      </div>\n    `;\n\n    // تسجيل النقرة\n    adElement.addEventListener('click', () => {\n      this.monetization.recordClick(ad.id, 'banner', 0.05); // 5 سنت لكل نقرة\n    });\n\n    this.adElements.set(containerId, adElement);\n    return adElement;\n  }\n\n  // إنشاء إعلان جانبي\n  createSidebarAd() {\n    const ad = this.monetization.getRandomAd('sidebar');\n    if (!ad || !this.monetization.adConfig.sidebarAds) return null;\n\n    const adElement = document.createElement('div');\n    adElement.className = 'integrated-sidebar-ad';\n    adElement.innerHTML = `\n      <div class=\"ad-content\" onclick=\"window.open('${ad.link}', '_blank')\">\n        <img src=\"${ad.image}\" alt=\"${ad.title}\" onerror=\"this.style.display='none'\">\n        <div class=\"ad-text\">\n          <h5>${ad.title}</h5>\n          <p>${ad.description}</p>\n        </div>\n        <div class=\"ad-label\">إعلان</div>\n      </div>\n    `;\n\n    adElement.addEventListener('click', () => {\n      this.monetization.recordClick(ad.id, 'sidebar', 0.03);\n    });\n\n    return adElement;\n  }\n\n  // إنشاء رابط تابع مدمج\n  createAffiliateLink(text, category = null) {\n    const link = this.monetization.getRandomAffiliateLink(category);\n    if (!link || !this.monetization.affiliateLinks.enabled) return text;\n\n    const linkElement = document.createElement('a');\n    linkElement.href = link.url;\n    linkElement.target = '_blank';\n    linkElement.textContent = text;\n    linkElement.className = 'affiliate-link';\n    linkElement.title = `${link.name} - عمولة ${link.commission}%`;\n\n    linkElement.addEventListener('click', () => {\n      this.monetization.recordClick(link.id, 'affiliate', 0.10);\n    });\n\n    return linkElement.outerHTML;\n  }\n\n  // بدء دوران الإعلانات\n  startAdRotation() {\n    setInterval(() => {\n      this.rotateAds();\n    }, this.rotationInterval);\n  }\n\n  // تدوير الإعلانات\n  rotateAds() {\n    this.adElements.forEach((element, containerId) => {\n      const container = document.getElementById(containerId);\n      if (container && element.parentNode === container) {\n        container.removeChild(element);\n        const newAd = this.createBannerAd(containerId);\n        if (newAd) {\n          container.appendChild(newAd);\n        }\n      }\n    });\n  }\n}\n\n// إنشاء مثيلات عامة\nexport const developerMonetization = new DeveloperMonetization();\nexport const integratedAds = new IntegratedAds(developerMonetization);\n\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,qBAAsB,CACjCC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,SAAS,CAAG,IAAI,CACrB,IAAI,CAACC,QAAQ,CAAG,CACdC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,KAAK,CACtBC,QAAQ,CAAE,KACZ,CAAC,CAED,IAAI,CAACC,cAAc,CAAG,CACpBC,OAAO,CAAE,IAAI,CACbC,KAAK,CAAE,CACL,CACEC,EAAE,CAAE,YAAY,CAChBC,IAAI,CAAE,cAAc,CACpBC,GAAG,CAAE,6CAA6C,CAClDC,QAAQ,CAAE,OAAO,CACjBC,UAAU,CAAE,CACd,CAAC,CACD,CACEJ,EAAE,CAAE,gBAAgB,CACpBC,IAAI,CAAE,gBAAgB,CACtBC,GAAG,CAAE,iDAAiD,CACtDC,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,EACd,CAAC,CAEL,CAAC,CAED,IAAI,CAACC,gBAAgB,CAAG,CACtBP,OAAO,CAAE,IAAI,CACbQ,OAAO,CAAE,CACP,CACEN,EAAE,CAAE,WAAW,CACfO,KAAK,CAAE,mBAAmB,CAC1BC,WAAW,CAAE,+BAA+B,CAC5CC,IAAI,CAAE,4CAA4C,CAClDC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,QACR,CAAC,CACD,CACEX,EAAE,CAAE,WAAW,CACfO,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CAAE,oCAAoC,CACjDC,IAAI,CAAE,gDAAgD,CACtDC,KAAK,CAAE,6BAA6B,CACpCC,IAAI,CAAE,SACR,CAAC,CAEL,CAAC,CAED,IAAI,CAACC,SAAS,CAAG,CACfC,WAAW,CAAE,CAAC,CACdC,MAAM,CAAE,CAAC,CACTC,WAAW,CAAE,CAAC,CACdC,OAAO,CAAE,CACX,CAAC,CAED,IAAI,CAACC,aAAa,CAAC,CAAC,CACtB,CAEA;AACAA,aAAaA,CAAA,CAAG,CACd,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,CACzD,GAAIF,KAAK,CAAE,CACT,GAAI,CACF,IAAI,CAACN,SAAS,CAAG,CAAE,GAAG,IAAI,CAACA,SAAS,CAAE,GAAGS,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAE,CAAC,CAC9D,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CACF,CAEA;AACAE,aAAaA,CAAA,CAAG,CACdN,YAAY,CAACO,OAAO,CAAC,qBAAqB,CAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC,CAC7E,CAEA;AACAgB,gBAAgBA,CAACC,IAAI,CAAEC,MAAM,CAAE,CAC7B,IAAI,CAAClB,SAAS,CAACC,WAAW,EAAE,CAC5B,IAAI,CAACY,aAAa,CAAC,CAAC,CAEpB;AACA,IAAI,CAACM,aAAa,CAAC,YAAY,CAAE,CAAEF,IAAI,CAAEC,MAAO,CAAC,CAAC,CACpD,CAEA;AACAE,WAAWA,CAACH,IAAI,CAAEC,MAAM,CAAe,IAAb,CAAAd,OAAO,CAAAiB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CACnC,IAAI,CAACrB,SAAS,CAACE,MAAM,EAAE,CACvB,IAAI,CAACF,SAAS,CAACI,OAAO,EAAIA,OAAO,CACjC,IAAI,CAACS,aAAa,CAAC,CAAC,CAEpB;AACA,IAAI,CAACM,aAAa,CAAC,OAAO,CAAE,CAAEF,IAAI,CAAEC,MAAM,CAAEd,OAAQ,CAAC,CAAC,CACxD,CAEA;AACAoB,gBAAgBA,CAACP,IAAI,CAAEb,OAAO,CAAE,CAC9B,IAAI,CAACJ,SAAS,CAACG,WAAW,EAAE,CAC5B,IAAI,CAACH,SAAS,CAACI,OAAO,EAAIA,OAAO,CACjC,IAAI,CAACS,aAAa,CAAC,CAAC,CAEpB,IAAI,CAACM,aAAa,CAAC,YAAY,CAAE,CAAEF,IAAI,CAAEb,OAAQ,CAAC,CAAC,CACrD,CAEA;AACAe,aAAaA,CAACM,KAAK,CAAEC,IAAI,CAAE,CACzB;AACAd,OAAO,CAACe,GAAG,CAAC,oBAAoBF,KAAK,EAAE,CAAEC,IAAI,CAAC,CAE9C;AACA,GAAIE,SAAS,CAACC,MAAM,CAAE,CACpB;AACA;AACA;AACA;AACA;AAAA,CAEJ,CAEA;AACAC,WAAWA,CAAA,CAAkB,IAAjB,CAAA/B,IAAI,CAAAsB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,QAAQ,CACzB,KAAM,CAAAU,GAAG,CAAG,IAAI,CAACtC,gBAAgB,CAACC,OAAO,CAACsC,MAAM,CAACC,EAAE,EAAIA,EAAE,CAAClC,IAAI,GAAKA,IAAI,CAAC,CACxE,GAAIgC,GAAG,CAACT,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEjC,KAAM,CAAAY,QAAQ,CAAGH,GAAG,CAACI,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGN,GAAG,CAACT,MAAM,CAAC,CAAC,CAC5D,IAAI,CAACN,gBAAgB,CAACkB,QAAQ,CAAC9C,EAAE,CAAEW,IAAI,CAAC,CAExC,MAAO,CAAAmC,QAAQ,CACjB,CAEA;AACAI,sBAAsBA,CAAA,CAAkB,IAAjB,CAAA/C,QAAQ,CAAA8B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACpC,GAAI,CAAAlC,KAAK,CAAG,IAAI,CAACF,cAAc,CAACE,KAAK,CAErC,GAAII,QAAQ,CAAE,CACZJ,KAAK,CAAGA,KAAK,CAAC6C,MAAM,CAACnC,IAAI,EAAIA,IAAI,CAACN,QAAQ,GAAKA,QAAQ,CAAC,CAC1D,CAEA,GAAIJ,KAAK,CAACmC,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEnC,MAAO,CAAAnC,KAAK,CAACgD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGlD,KAAK,CAACmC,MAAM,CAAC,CAAC,CACxD,CAEA;AACAiB,mBAAmBA,CAAC7C,OAAO,CAAE,CAC3B,IAAI,CAACD,gBAAgB,CAACC,OAAO,CAAC8C,IAAI,CAAC,CACjC,GAAG9C,OAAO,CACVN,EAAE,CAAE,WAAWqD,IAAI,CAACC,GAAG,CAAC,CAAC,EAC3B,CAAC,CAAC,CACJ,CAEA;AACAC,gBAAgBA,CAAC9C,IAAI,CAAE,CACrB,IAAI,CAACZ,cAAc,CAACE,KAAK,CAACqD,IAAI,CAAC,CAC7B,GAAG3C,IAAI,CACPT,EAAE,CAAE,aAAaqD,IAAI,CAACC,GAAG,CAAC,CAAC,EAC7B,CAAC,CAAC,CACJ,CAEA;AACAE,cAAcA,CAACC,MAAM,CAAE,CACrB,IAAI,CAACjE,QAAQ,CAAG,CAAE,GAAG,IAAI,CAACA,QAAQ,CAAE,GAAGiE,MAAO,CAAC,CACjD,CAEA;AACAC,YAAYA,CAAA,CAAG,CACb,KAAM,CAAAC,GAAG,CAAG,IAAI,CAAC/C,SAAS,CAACC,WAAW,CAAG,CAAC,CACtC,CAAC,IAAI,CAACD,SAAS,CAACE,MAAM,CAAG,IAAI,CAACF,SAAS,CAACC,WAAW,CAAG,GAAG,EAAE+C,OAAO,CAAC,CAAC,CAAC,CACrE,CAAC,CAEL,KAAM,CAAAC,cAAc,CAAG,IAAI,CAACjD,SAAS,CAACE,MAAM,CAAG,CAAC,CAC5C,CAAC,IAAI,CAACF,SAAS,CAACG,WAAW,CAAG,IAAI,CAACH,SAAS,CAACE,MAAM,CAAG,GAAG,EAAE8C,OAAO,CAAC,CAAC,CAAC,CACrE,CAAC,CAEL,MAAO,CACL,GAAG,IAAI,CAAChD,SAAS,CACjB+C,GAAG,CAAE,GAAGA,GAAG,GAAG,CACdE,cAAc,CAAE,GAAGA,cAAc,GAAG,CACpCC,gBAAgB,CAAE,GAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAC/CC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KACZ,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAACvD,SAAS,CAACI,OAAO,CAClC,CAAC,CACH,CAEA;AACAoD,MAAMA,CAAA,CAAG,CACP,IAAI,CAAC7E,SAAS,CAAG,CAAC,IAAI,CAACA,SAAS,CAChC,MAAO,KAAI,CAACA,SAAS,CACvB,CACF,CAEA;AACA,MAAO,MAAM,CAAA8E,aAAc,CACzB/E,WAAWA,CAACgF,YAAY,CAAE,CACxB,IAAI,CAACA,YAAY,CAAGA,YAAY,CAChC,IAAI,CAACC,UAAU,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC3B,IAAI,CAACC,gBAAgB,CAAG,KAAK,CAAE;AACjC,CAEA;AACAC,cAAcA,CAACC,WAAW,CAAE,CAC1B,KAAM,CAAA9B,EAAE,CAAG,IAAI,CAACyB,YAAY,CAAC5B,WAAW,CAAC,QAAQ,CAAC,CAClD,GAAI,CAACG,EAAE,EAAI,CAAC,IAAI,CAACyB,YAAY,CAAC9E,QAAQ,CAACC,SAAS,CAAE,MAAO,KAAI,CAE7D,KAAM,CAAAmF,SAAS,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC/CF,SAAS,CAACG,SAAS,CAAG,sBAAsB,CAC5CH,SAAS,CAACI,SAAS,CAAG;AAC1B,sDAAsDnC,EAAE,CAACpC,IAAI;AAC7D,oBAAoBoC,EAAE,CAACnC,KAAK,UAAUmC,EAAE,CAACtC,KAAK;AAC9C;AACA,gBAAgBsC,EAAE,CAACtC,KAAK;AACxB,eAAesC,EAAE,CAACrC,WAAW;AAC7B;AACA;AACA;AACA,KAAK,CAED;AACAoE,SAAS,CAACK,gBAAgB,CAAC,OAAO,CAAE,IAAM,CACxC,IAAI,CAACX,YAAY,CAACtC,WAAW,CAACa,EAAE,CAAC7C,EAAE,CAAE,QAAQ,CAAE,IAAI,CAAC,CAAE;AACxD,CAAC,CAAC,CAEF,IAAI,CAACuE,UAAU,CAACW,GAAG,CAACP,WAAW,CAAEC,SAAS,CAAC,CAC3C,MAAO,CAAAA,SAAS,CAClB,CAEA;AACAO,eAAeA,CAAA,CAAG,CAChB,KAAM,CAAAtC,EAAE,CAAG,IAAI,CAACyB,YAAY,CAAC5B,WAAW,CAAC,SAAS,CAAC,CACnD,GAAI,CAACG,EAAE,EAAI,CAAC,IAAI,CAACyB,YAAY,CAAC9E,QAAQ,CAACE,UAAU,CAAE,MAAO,KAAI,CAE9D,KAAM,CAAAkF,SAAS,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC/CF,SAAS,CAACG,SAAS,CAAG,uBAAuB,CAC7CH,SAAS,CAACI,SAAS,CAAG;AAC1B,sDAAsDnC,EAAE,CAACpC,IAAI;AAC7D,oBAAoBoC,EAAE,CAACnC,KAAK,UAAUmC,EAAE,CAACtC,KAAK;AAC9C;AACA,gBAAgBsC,EAAE,CAACtC,KAAK;AACxB,eAAesC,EAAE,CAACrC,WAAW;AAC7B;AACA;AACA;AACA,KAAK,CAEDoE,SAAS,CAACK,gBAAgB,CAAC,OAAO,CAAE,IAAM,CACxC,IAAI,CAACX,YAAY,CAACtC,WAAW,CAACa,EAAE,CAAC7C,EAAE,CAAE,SAAS,CAAE,IAAI,CAAC,CACvD,CAAC,CAAC,CAEF,MAAO,CAAA4E,SAAS,CAClB,CAEA;AACAQ,mBAAmBA,CAACC,IAAI,CAAmB,IAAjB,CAAAlF,QAAQ,CAAA8B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACvC,KAAM,CAAAxB,IAAI,CAAG,IAAI,CAAC6D,YAAY,CAACpB,sBAAsB,CAAC/C,QAAQ,CAAC,CAC/D,GAAI,CAACM,IAAI,EAAI,CAAC,IAAI,CAAC6D,YAAY,CAACzE,cAAc,CAACC,OAAO,CAAE,MAAO,CAAAuF,IAAI,CAEnE,KAAM,CAAAC,WAAW,CAAGT,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CAC/CQ,WAAW,CAACC,IAAI,CAAG9E,IAAI,CAACP,GAAG,CAC3BoF,WAAW,CAACE,MAAM,CAAG,QAAQ,CAC7BF,WAAW,CAACG,WAAW,CAAGJ,IAAI,CAC9BC,WAAW,CAACP,SAAS,CAAG,gBAAgB,CACxCO,WAAW,CAAC/E,KAAK,CAAG,GAAGE,IAAI,CAACR,IAAI,YAAYQ,IAAI,CAACL,UAAU,GAAG,CAE9DkF,WAAW,CAACL,gBAAgB,CAAC,OAAO,CAAE,IAAM,CAC1C,IAAI,CAACX,YAAY,CAACtC,WAAW,CAACvB,IAAI,CAACT,EAAE,CAAE,WAAW,CAAE,IAAI,CAAC,CAC3D,CAAC,CAAC,CAEF,MAAO,CAAAsF,WAAW,CAACI,SAAS,CAC9B,CAEA;AACAC,eAAeA,CAAA,CAAG,CAChBC,WAAW,CAAC,IAAM,CAChB,IAAI,CAACC,SAAS,CAAC,CAAC,CAClB,CAAC,CAAE,IAAI,CAACpB,gBAAgB,CAAC,CAC3B,CAEA;AACAoB,SAASA,CAAA,CAAG,CACV,IAAI,CAACtB,UAAU,CAACuB,OAAO,CAAC,CAACC,OAAO,CAAEpB,WAAW,GAAK,CAChD,KAAM,CAAAqB,SAAS,CAAGnB,QAAQ,CAACoB,cAAc,CAACtB,WAAW,CAAC,CACtD,GAAIqB,SAAS,EAAID,OAAO,CAACG,UAAU,GAAKF,SAAS,CAAE,CACjDA,SAAS,CAACG,WAAW,CAACJ,OAAO,CAAC,CAC9B,KAAM,CAAAK,KAAK,CAAG,IAAI,CAAC1B,cAAc,CAACC,WAAW,CAAC,CAC9C,GAAIyB,KAAK,CAAE,CACTJ,SAAS,CAACK,WAAW,CAACD,KAAK,CAAC,CAC9B,CACF,CACF,CAAC,CAAC,CACJ,CACF,CAEA;AACA,MAAO,MAAM,CAAAE,qBAAqB,CAAG,GAAI,CAAAjH,qBAAqB,CAAC,CAAC,CAChE,MAAO,MAAM,CAAAkH,aAAa,CAAG,GAAI,CAAAlC,aAAa,CAACiC,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}