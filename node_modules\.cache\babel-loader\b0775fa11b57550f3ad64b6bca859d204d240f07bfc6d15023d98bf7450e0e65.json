{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./App.css';import Header from'./components/Header';import YouTubePlayerEnhanced from'./components/YouTubePlayerEnhanced';import SettingsEnhanced from'./components/SettingsEnhanced';import SidebarEnhanced from'./components/SidebarEnhanced';import DeveloperInfo from'./components/DeveloperInfo';import{YouTubeHelper}from'./utils/youtubeApi';import{adBlocker}from'./utils/adBlocker';import{developerMonetization,integratedAds}from'./utils/developerMonetization';import{channelConfig,loadChannelVideos,recordView}from'./utils/channelConfig';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AppFinal(){const[currentVideoId,setCurrentVideoId]=useState('');const[showSettings,setShowSettings]=useState(false);const[showDeveloperInfo,setShowDeveloperInfo]=useState(false);const[channelVideos,setChannelVideos]=useState([]);const[channelInfo,setChannelInfo]=useState(null);const[isLoadingChannel,setIsLoadingChannel]=useState(true);const[settings,setSettings]=useState({quality:'auto',hideControls:false,adBlock:true,darkTheme:true,volume:100,autoplay:false});const[searchQuery,setSearchQuery]=useState('');const[appStats,setAppStats]=useState({videosWatched:0,adsBlocked:0,timeSpent:0});// تحميل الإعدادات من التخزين المحلي\nuseEffect(()=>{const savedSettings=localStorage.getItem('youtube-player-settings');if(savedSettings){try{const parsed=JSON.parse(savedSettings);setSettings(prev=>({...prev,...parsed}));}catch(error){console.error('Error loading settings:',error);}}const savedStats=localStorage.getItem('youtube-player-stats');if(savedStats){try{const parsed=JSON.parse(savedStats);setAppStats(prev=>({...prev,...parsed}));}catch(error){console.error('Error loading stats:',error);}}// تحميل القناة الافتراضية عند بدء التطبيق\nloadDefaultChannel();// بدء دوران الإعلانات\nintegratedAds.startAdRotation();},[]);// تحميل القناة الافتراضية\nconst loadDefaultChannel=async()=>{try{setIsLoadingChannel(true);const channelData=await loadChannelVideos(channelConfig.defaultChannel.channelId);setChannelInfo(channelData.channelInfo);setChannelVideos(channelData.videos);// تشغيل أول فيديو تلقائياً إذا كان متاحاً\nif(channelData.videos.length>0&&settings.autoplay){const firstVideo=channelData.videos[0];setCurrentVideoId(firstVideo.id);recordView(firstVideo.id,channelConfig.defaultChannel.channelId);}}catch(error){console.error('Error loading default channel:',error);}finally{setIsLoadingChannel(false);}};// حفظ الإعدادات في التخزين المحلي\nuseEffect(()=>{localStorage.setItem('youtube-player-settings',JSON.stringify(settings));},[settings]);// حفظ الإحصائيات في التخزين المحلي\nuseEffect(()=>{localStorage.setItem('youtube-player-stats',JSON.stringify(appStats));},[appStats]);// معالجة البحث أو تشغيل الفيديو\nconst handleVideoLoad=input=>{const videoId=YouTubeHelper.extractVideoId(input);if(videoId){setCurrentVideoId(videoId);setSearchQuery('');// تحديث إحصائية الفيديوهات المشاهدة\nsetAppStats(prev=>({...prev,videosWatched:prev.videosWatched+1}));// إرسال حدث بدء الفيديو لنظام الربح\nwindow.dispatchEvent(new CustomEvent('video-started',{detail:{videoId}}));}else{// إذا لم يكن رابط، فهو استعلام بحث\nsetSearchQuery(input);}};// تحديث الإعدادات\nconst updateSettings=newSettings=>{setSettings(prev=>({...prev,...newSettings}));};// معالجة اختيار فيديو من الشريط الجانبي\nconst handleVideoSelect=videoId=>{setCurrentVideoId(videoId);setSearchQuery('');// تحديث إحصائية الفيديوهات المشاهدة\nsetAppStats(prev=>({...prev,videosWatched:prev.videosWatched+1}));// تسجيل المشاهدة للقناة الافتراضية\nrecordView(videoId,channelConfig.defaultChannel.channelId);// إرسال حدث بدء الفيديو\nwindow.dispatchEvent(new CustomEvent('video-started',{detail:{videoId}}));};// محاكاة حجب الإعلانات وتحديث الإحصائيات\nuseEffect(()=>{if(settings.adBlock&&currentVideoId){const interval=setInterval(()=>{// محاكاة حجب إعلانات\nconst adsBlocked=Math.floor(Math.random()*3)+1;setAppStats(prev=>({...prev,adsBlocked:prev.adsBlocked+adsBlocked}));// تسجيل الإعلانات المحجوبة في نظام مانع الإعلانات\nfor(let i=0;i<adsBlocked;i++){adBlocker.blockAd(`https://ads.youtube.com/fake-ad-${Date.now()}-${i}`);}},15000);// كل 15 ثانية\nreturn()=>clearInterval(interval);}},[settings.adBlock,currentVideoId]);// تتبع الوقت المقضي\nuseEffect(()=>{const interval=setInterval(()=>{setAppStats(prev=>({...prev,timeSpent:prev.timeSpent+1}));},60000);// كل دقيقة\nreturn()=>clearInterval(interval);},[]);// إضافة الإعلانات المدمجة للشريط الجانبي\nconst renderSidebarWithAds=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-with-ads\",children:[/*#__PURE__*/_jsx(SidebarEnhanced,{searchQuery:searchQuery,onVideoSelect:handleVideoSelect,currentVideoId:currentVideoId,channelVideos:channelVideos,channelInfo:channelInfo,isLoadingChannel:isLoadingChannel}),developerMonetization.adConfig.sidebarAds&&/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-ad-container\",children:/*#__PURE__*/_jsx(SidebarAd,{})})]});};// مكون الإعلان الجانبي\nconst SidebarAd=()=>{const[ad,setAd]=useState(null);useEffect(()=>{const adElement=integratedAds.createSidebarAd();if(adElement){setAd(adElement.outerHTML);}},[]);if(!ad)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-ad\",dangerouslySetInnerHTML:{__html:ad}});};// مكون الإعلان البانر\nconst BannerAd=()=>{const[ad,setAd]=useState(null);useEffect(()=>{const adElement=integratedAds.createBannerAd('banner-container');if(adElement){setAd(adElement.outerHTML);}},[currentVideoId]);// تغيير الإعلان مع كل فيديو جديد\nif(!ad||!developerMonetization.adConfig.bannerAds)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"banner-ad\",dangerouslySetInnerHTML:{__html:ad}});};const formatTime=minutes=>{const hours=Math.floor(minutes/60);const mins=minutes%60;if(hours>0){return`${hours}س ${mins}د`;}return`${mins}د`;};return/*#__PURE__*/_jsxs(\"div\",{className:`app ${settings.darkTheme?'dark-theme':'light-theme'}`,children:[/*#__PURE__*/_jsx(Header,{onVideoLoad:handleVideoLoad,onSettingsClick:()=>setShowSettings(!showSettings),stats:appStats}),/*#__PURE__*/_jsx(BannerAd,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"app-content\",children:[renderSidebarWithAds(),/*#__PURE__*/_jsxs(\"main\",{className:\"main-content\",children:[/*#__PURE__*/_jsx(YouTubePlayerEnhanced,{videoId:currentVideoId,settings:settings}),currentVideoId&&/*#__PURE__*/_jsxs(\"div\",{className:\"status-bar\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFA5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0646\\u0634\\u0637\"})]}),settings.adBlock&&/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDEE1\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u0645\\u0627\\u0646\\u0639 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0645\\u0641\\u0639\\u0644\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u062C\\u0648\\u062F\\u0629: \",YouTubeHelper.getQualityLabel(settings.quality)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsxs(\"span\",{children:[appStats.adsBlocked,\" \\u0625\\u0639\\u0644\\u0627\\u0646 \\u0645\\u062D\\u062C\\u0648\\u0628\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCB0\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u0623\\u0631\\u0628\\u0627\\u062D \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631: \",developerMonetization.getAnalytics().revenueFormatted]})]})]})]}),showSettings&&/*#__PURE__*/_jsx(SettingsEnhanced,{settings:settings,onSettingsChange:updateSettings,onClose:()=>setShowSettings(false),stats:appStats})]}),appStats.videosWatched===0&&!currentVideoId&&!isLoadingChannel&&/*#__PURE__*/_jsx(\"div\",{className:\"welcome-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-content\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83C\\uDF89 \\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0645\\u0634\\u063A\\u0644 \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628 \\u0627\\u0644\\u0645\\u062A\\u0642\\u062F\\u0645!\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0627\\u0633\\u062A\\u0645\\u062A\\u0639 \\u0628\\u062A\\u062C\\u0631\\u0628\\u0629 \\u0645\\u0634\\u0627\\u0647\\u062F\\u0629 \\u0645\\u062D\\u0633\\u0646\\u0629 \\u0645\\u0639:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDEAB \\u062D\\u062C\\u0628 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83C\\uDFA8 \\u0648\\u0627\\u062C\\u0647\\u0629 \\u0639\\u0631\\u0628\\u064A\\u0629 \\u0623\\u0646\\u064A\\u0642\\u0629\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2699\\uFE0F \\u062A\\u062D\\u0643\\u0645 \\u0643\\u0627\\u0645\\u0644 \\u0641\\u064A \\u0627\\u0644\\u062C\\u0648\\u062F\\u0629\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDCF1 \\u062A\\u0635\\u0645\\u064A\\u0645 \\u0645\\u062A\\u062C\\u0627\\u0648\\u0628\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDCB0 \\u062F\\u0639\\u0645 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631 \\u0628\\u0637\\u0631\\u064A\\u0642\\u0629 \\u063A\\u064A\\u0631 \\u0645\\u0632\\u0639\\u062C\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"welcome-btn\",onClick:()=>setAppStats(prev=>({...prev,videosWatched:1})),children:\"\\u0627\\u0628\\u062F\\u0623 \\u0627\\u0644\\u0622\\u0646\"}),/*#__PURE__*/_jsx(\"button\",{className:\"developer-btn\",onClick:()=>setShowDeveloperInfo(true),children:\"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\"})]})]})}),settings.adBlock&&currentVideoId&&/*#__PURE__*/_jsx(\"div\",{className:\"ad-block-notification\",children:/*#__PURE__*/_jsxs(\"span\",{children:[\"\\uD83D\\uDEE1\\uFE0F \\u062A\\u0645 \\u062D\\u062C\\u0628 \",appStats.adsBlocked,\" \\u0625\\u0639\\u0644\\u0627\\u0646\"]})}),showDeveloperInfo&&/*#__PURE__*/_jsx(DeveloperInfo,{onClose:()=>setShowDeveloperInfo(false)}),/*#__PURE__*/_jsx(DeveloperInfo,{compact:true})]});}export default AppFinal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "YouTubePlayerEnhanced", "SettingsEnhanced", "SidebarEnhanced", "DeveloperInfo", "YouTubeHelper", "ad<PERSON><PERSON><PERSON>", "developerMonetization", "integratedAds", "channelConfig", "loadChannelVideos", "recordView", "jsx", "_jsx", "jsxs", "_jsxs", "AppFinal", "currentVideoId", "setCurrentVideoId", "showSettings", "setShowSettings", "showDeveloperInfo", "setShowDeveloperInfo", "channelVideos", "setChannelVideos", "channelInfo", "setChannelInfo", "isLoadingChannel", "setIsLoadingChannel", "settings", "setSettings", "quality", "hideControls", "adBlock", "darkTheme", "volume", "autoplay", "searchQuery", "setSearch<PERSON>uery", "appStats", "setAppStats", "videosWatched", "adsBlocked", "timeSpent", "savedSettings", "localStorage", "getItem", "parsed", "JSON", "parse", "prev", "error", "console", "savedStats", "loadDefaultChannel", "startAdRotation", "channelData", "defaultChannel", "channelId", "videos", "length", "firstVideo", "id", "setItem", "stringify", "handleVideoLoad", "input", "videoId", "extractVideoId", "window", "dispatchEvent", "CustomEvent", "detail", "updateSettings", "newSettings", "handleVideoSelect", "interval", "setInterval", "Math", "floor", "random", "i", "blockAd", "Date", "now", "clearInterval", "renderSidebarWithAds", "className", "children", "onVideoSelect", "adConfig", "sidebarAds", "SidebarAd", "ad", "setAd", "adElement", "createSidebarAd", "outerHTML", "dangerouslySetInnerHTML", "__html", "BannerAd", "createBannerAd", "bannerAds", "formatTime", "minutes", "hours", "mins", "onVideoLoad", "onSettingsClick", "stats", "getQualityLabel", "getAnalytics", "revenueFormatted", "onSettingsChange", "onClose", "onClick", "compact"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/AppFinal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './App.css';\nimport Header from './components/Header';\nimport YouTubePlayerEnhanced from './components/YouTubePlayerEnhanced';\nimport SettingsEnhanced from './components/SettingsEnhanced';\nimport SidebarEnhanced from './components/SidebarEnhanced';\nimport DeveloperInfo from './components/DeveloperInfo';\nimport { YouTubeHelper } from './utils/youtubeApi';\nimport { adBlocker } from './utils/adBlocker';\nimport { developerMonetization, integratedAds } from './utils/developerMonetization';\nimport { channelConfig, loadChannelVideos, recordView } from './utils/channelConfig';\n\nfunction AppFinal() {\n  const [currentVideoId, setCurrentVideoId] = useState('');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showDeveloperInfo, setShowDeveloperInfo] = useState(false);\n  const [channelVideos, setChannelVideos] = useState([]);\n  const [channelInfo, setChannelInfo] = useState(null);\n  const [isLoadingChannel, setIsLoadingChannel] = useState(true);\n  const [settings, setSettings] = useState({\n    quality: 'auto',\n    hideControls: false,\n    adBlock: true,\n    darkTheme: true,\n    volume: 100,\n    autoplay: false\n  });\n  const [searchQuery, setSearchQuery] = useState('');\n  const [appStats, setAppStats] = useState({\n    videosWatched: 0,\n    adsBlocked: 0,\n    timeSpent: 0\n  });\n\n  // تحميل الإعدادات من التخزين المحلي\n  useEffect(() => {\n    const savedSettings = localStorage.getItem('youtube-player-settings');\n    if (savedSettings) {\n      try {\n        const parsed = JSON.parse(savedSettings);\n        setSettings(prev => ({ ...prev, ...parsed }));\n      } catch (error) {\n        console.error('Error loading settings:', error);\n      }\n    }\n\n    const savedStats = localStorage.getItem('youtube-player-stats');\n    if (savedStats) {\n      try {\n        const parsed = JSON.parse(savedStats);\n        setAppStats(prev => ({ ...prev, ...parsed }));\n      } catch (error) {\n        console.error('Error loading stats:', error);\n      }\n    }\n\n    // تحميل القناة الافتراضية عند بدء التطبيق\n    loadDefaultChannel();\n\n    // بدء دوران الإعلانات\n    integratedAds.startAdRotation();\n  }, []);\n\n  // تحميل القناة الافتراضية\n  const loadDefaultChannel = async () => {\n    try {\n      setIsLoadingChannel(true);\n      const channelData = await loadChannelVideos(channelConfig.defaultChannel.channelId);\n      \n      setChannelInfo(channelData.channelInfo);\n      setChannelVideos(channelData.videos);\n      \n      // تشغيل أول فيديو تلقائياً إذا كان متاحاً\n      if (channelData.videos.length > 0 && settings.autoplay) {\n        const firstVideo = channelData.videos[0];\n        setCurrentVideoId(firstVideo.id);\n        recordView(firstVideo.id, channelConfig.defaultChannel.channelId);\n      }\n    } catch (error) {\n      console.error('Error loading default channel:', error);\n    } finally {\n      setIsLoadingChannel(false);\n    }\n  };\n\n  // حفظ الإعدادات في التخزين المحلي\n  useEffect(() => {\n    localStorage.setItem('youtube-player-settings', JSON.stringify(settings));\n  }, [settings]);\n\n  // حفظ الإحصائيات في التخزين المحلي\n  useEffect(() => {\n    localStorage.setItem('youtube-player-stats', JSON.stringify(appStats));\n  }, [appStats]);\n\n  // معالجة البحث أو تشغيل الفيديو\n  const handleVideoLoad = (input) => {\n    const videoId = YouTubeHelper.extractVideoId(input);\n    if (videoId) {\n      setCurrentVideoId(videoId);\n      setSearchQuery('');\n      \n      // تحديث إحصائية الفيديوهات المشاهدة\n      setAppStats(prev => ({\n        ...prev,\n        videosWatched: prev.videosWatched + 1\n      }));\n\n      // إرسال حدث بدء الفيديو لنظام الربح\n      window.dispatchEvent(new CustomEvent('video-started', { detail: { videoId } }));\n    } else {\n      // إذا لم يكن رابط، فهو استعلام بحث\n      setSearchQuery(input);\n    }\n  };\n\n  // تحديث الإعدادات\n  const updateSettings = (newSettings) => {\n    setSettings(prev => ({ ...prev, ...newSettings }));\n  };\n\n  // معالجة اختيار فيديو من الشريط الجانبي\n  const handleVideoSelect = (videoId) => {\n    setCurrentVideoId(videoId);\n    setSearchQuery('');\n    \n    // تحديث إحصائية الفيديوهات المشاهدة\n    setAppStats(prev => ({\n      ...prev,\n      videosWatched: prev.videosWatched + 1\n    }));\n\n    // تسجيل المشاهدة للقناة الافتراضية\n    recordView(videoId, channelConfig.defaultChannel.channelId);\n\n    // إرسال حدث بدء الفيديو\n    window.dispatchEvent(new CustomEvent('video-started', { detail: { videoId } }));\n  };\n\n  // محاكاة حجب الإعلانات وتحديث الإحصائيات\n  useEffect(() => {\n    if (settings.adBlock && currentVideoId) {\n      const interval = setInterval(() => {\n        // محاكاة حجب إعلانات\n        const adsBlocked = Math.floor(Math.random() * 3) + 1;\n        \n        setAppStats(prev => ({\n          ...prev,\n          adsBlocked: prev.adsBlocked + adsBlocked\n        }));\n\n        // تسجيل الإعلانات المحجوبة في نظام مانع الإعلانات\n        for (let i = 0; i < adsBlocked; i++) {\n          adBlocker.blockAd(`https://ads.youtube.com/fake-ad-${Date.now()}-${i}`);\n        }\n      }, 15000); // كل 15 ثانية\n\n      return () => clearInterval(interval);\n    }\n  }, [settings.adBlock, currentVideoId]);\n\n  // تتبع الوقت المقضي\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAppStats(prev => ({\n        ...prev,\n        timeSpent: prev.timeSpent + 1\n      }));\n    }, 60000); // كل دقيقة\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // إضافة الإعلانات المدمجة للشريط الجانبي\n  const renderSidebarWithAds = () => {\n    return (\n      <div className=\"sidebar-with-ads\">\n        <SidebarEnhanced \n          searchQuery={searchQuery}\n          onVideoSelect={handleVideoSelect}\n          currentVideoId={currentVideoId}\n          channelVideos={channelVideos}\n          channelInfo={channelInfo}\n          isLoadingChannel={isLoadingChannel}\n        />\n        \n        {/* إعلان جانبي مدمج */}\n        {developerMonetization.adConfig.sidebarAds && (\n          <div className=\"sidebar-ad-container\">\n            <SidebarAd />\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  // مكون الإعلان الجانبي\n  const SidebarAd = () => {\n    const [ad, setAd] = useState(null);\n\n    useEffect(() => {\n      const adElement = integratedAds.createSidebarAd();\n      if (adElement) {\n        setAd(adElement.outerHTML);\n      }\n    }, []);\n\n    if (!ad) return null;\n\n    return (\n      <div \n        className=\"sidebar-ad\"\n        dangerouslySetInnerHTML={{ __html: ad }}\n      />\n    );\n  };\n\n  // مكون الإعلان البانر\n  const BannerAd = () => {\n    const [ad, setAd] = useState(null);\n\n    useEffect(() => {\n      const adElement = integratedAds.createBannerAd('banner-container');\n      if (adElement) {\n        setAd(adElement.outerHTML);\n      }\n    }, [currentVideoId]); // تغيير الإعلان مع كل فيديو جديد\n\n    if (!ad || !developerMonetization.adConfig.bannerAds) return null;\n\n    return (\n      <div \n        className=\"banner-ad\"\n        dangerouslySetInnerHTML={{ __html: ad }}\n      />\n    );\n  };\n\n  const formatTime = (minutes) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}س ${mins}د`;\n    }\n    return `${mins}د`;\n  };\n\n  return (\n    <div className={`app ${settings.darkTheme ? 'dark-theme' : 'light-theme'}`}>\n      <Header \n        onVideoLoad={handleVideoLoad}\n        onSettingsClick={() => setShowSettings(!showSettings)}\n        stats={appStats}\n      />\n      \n      {/* إعلان بانر علوي */}\n      <BannerAd />\n      \n      <div className=\"app-content\">\n        {renderSidebarWithAds()}\n        \n        <main className=\"main-content\">\n          <YouTubePlayerEnhanced \n            videoId={currentVideoId}\n            settings={settings}\n          />\n          \n          {/* شريط الحالة المحسن */}\n          {currentVideoId && (\n            <div className=\"status-bar\">\n              <div className=\"status-item\">\n                <span>🎥</span>\n                <span>فيديو نشط</span>\n              </div>\n              \n              {settings.adBlock && (\n                <div className=\"status-item\">\n                  <span>🛡️</span>\n                  <span>مانع الإعلانات مفعل</span>\n                </div>\n              )}\n              \n              <div className=\"status-item\">\n                <span>⚙️</span>\n                <span>جودة: {YouTubeHelper.getQualityLabel(settings.quality)}</span>\n              </div>\n              \n              <div className=\"status-item\">\n                <span>📊</span>\n                <span>{appStats.adsBlocked} إعلان محجوب</span>\n              </div>\n\n              <div className=\"status-item\">\n                <span>💰</span>\n                <span>أرباح المطور: {developerMonetization.getAnalytics().revenueFormatted}</span>\n              </div>\n            </div>\n          )}\n        </main>\n        \n        {showSettings && (\n          <SettingsEnhanced \n            settings={settings}\n            onSettingsChange={updateSettings}\n            onClose={() => setShowSettings(false)}\n            stats={appStats}\n          />\n        )}\n      </div>\n      \n      {/* رسالة ترحيب للمستخدمين الجدد */}\n      {appStats.videosWatched === 0 && !currentVideoId && !isLoadingChannel && (\n        <div className=\"welcome-overlay\">\n          <div className=\"welcome-content\">\n            <h2>🎉 مرحباً بك في مشغل يوتيوب المتقدم!</h2>\n            <p>استمتع بتجربة مشاهدة محسنة مع:</p>\n            <ul>\n              <li>🚫 حجب الإعلانات تلقائياً</li>\n              <li>🎨 واجهة عربية أنيقة</li>\n              <li>⚙️ تحكم كامل في الجودة</li>\n              <li>📱 تصميم متجاوب</li>\n              <li>💰 دعم المطور بطريقة غير مزعجة</li>\n            </ul>\n            <div className=\"welcome-buttons\">\n              <button \n                className=\"welcome-btn\"\n                onClick={() => setAppStats(prev => ({ ...prev, videosWatched: 1 }))}\n              >\n                ابدأ الآن\n              </button>\n              <button \n                className=\"developer-btn\"\n                onClick={() => setShowDeveloperInfo(true)}\n              >\n                معلومات المطور\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* إشعار حجب الإعلانات */}\n      {settings.adBlock && currentVideoId && (\n        <div className=\"ad-block-notification\">\n          <span>🛡️ تم حجب {appStats.adsBlocked} إعلان</span>\n        </div>\n      )}\n\n      {/* معلومات المطور */}\n      {showDeveloperInfo && (\n        <DeveloperInfo onClose={() => setShowDeveloperInfo(false)} />\n      )}\n\n      {/* معلومات المطور المدمجة */}\n      <DeveloperInfo compact={true} />\n    </div>\n  );\n}\n\nexport default AppFinal;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,WAAW,CAClB,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,qBAAqB,KAAM,oCAAoC,CACtE,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,OAASC,aAAa,KAAQ,oBAAoB,CAClD,OAASC,SAAS,KAAQ,mBAAmB,CAC7C,OAASC,qBAAqB,CAAEC,aAAa,KAAQ,+BAA+B,CACpF,OAASC,aAAa,CAAEC,iBAAiB,CAAEC,UAAU,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErF,QAAS,CAAAC,QAAQA,CAAA,CAAG,CAClB,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACqB,YAAY,CAAEC,eAAe,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACuB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACyB,aAAa,CAAEC,gBAAgB,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC2B,WAAW,CAAEC,cAAc,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAC6B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9B,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAAC+B,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,CACvCiC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,IAAI,CACfC,MAAM,CAAE,GAAG,CACXC,QAAQ,CAAE,KACZ,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACyC,QAAQ,CAAEC,WAAW,CAAC,CAAG1C,QAAQ,CAAC,CACvC2C,aAAa,CAAE,CAAC,CAChBC,UAAU,CAAE,CAAC,CACbC,SAAS,CAAE,CACb,CAAC,CAAC,CAEF;AACA5C,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6C,aAAa,CAAGC,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC,CACrE,GAAIF,aAAa,CAAE,CACjB,GAAI,CACF,KAAM,CAAAG,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC,CACxCd,WAAW,CAACoB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,GAAGH,MAAO,CAAC,CAAC,CAAC,CAC/C,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAEA,KAAM,CAAAE,UAAU,CAAGR,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAC/D,GAAIO,UAAU,CAAE,CACd,GAAI,CACF,KAAM,CAAAN,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACI,UAAU,CAAC,CACrCb,WAAW,CAACU,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,GAAGH,MAAO,CAAC,CAAC,CAAC,CAC/C,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAEA;AACAG,kBAAkB,CAAC,CAAC,CAEpB;AACA9C,aAAa,CAAC+C,eAAe,CAAC,CAAC,CACjC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAD,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF1B,mBAAmB,CAAC,IAAI,CAAC,CACzB,KAAM,CAAA4B,WAAW,CAAG,KAAM,CAAA9C,iBAAiB,CAACD,aAAa,CAACgD,cAAc,CAACC,SAAS,CAAC,CAEnFhC,cAAc,CAAC8B,WAAW,CAAC/B,WAAW,CAAC,CACvCD,gBAAgB,CAACgC,WAAW,CAACG,MAAM,CAAC,CAEpC;AACA,GAAIH,WAAW,CAACG,MAAM,CAACC,MAAM,CAAG,CAAC,EAAI/B,QAAQ,CAACO,QAAQ,CAAE,CACtD,KAAM,CAAAyB,UAAU,CAAGL,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC,CACxCzC,iBAAiB,CAAC2C,UAAU,CAACC,EAAE,CAAC,CAChCnD,UAAU,CAACkD,UAAU,CAACC,EAAE,CAAErD,aAAa,CAACgD,cAAc,CAACC,SAAS,CAAC,CACnE,CACF,CAAE,MAAOP,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CAAC,OAAS,CACRvB,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAED;AACA7B,SAAS,CAAC,IAAM,CACd8C,YAAY,CAACkB,OAAO,CAAC,yBAAyB,CAAEf,IAAI,CAACgB,SAAS,CAACnC,QAAQ,CAAC,CAAC,CAC3E,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACA9B,SAAS,CAAC,IAAM,CACd8C,YAAY,CAACkB,OAAO,CAAC,sBAAsB,CAAEf,IAAI,CAACgB,SAAS,CAACzB,QAAQ,CAAC,CAAC,CACxE,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAA0B,eAAe,CAAIC,KAAK,EAAK,CACjC,KAAM,CAAAC,OAAO,CAAG9D,aAAa,CAAC+D,cAAc,CAACF,KAAK,CAAC,CACnD,GAAIC,OAAO,CAAE,CACXjD,iBAAiB,CAACiD,OAAO,CAAC,CAC1B7B,cAAc,CAAC,EAAE,CAAC,CAElB;AACAE,WAAW,CAACU,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPT,aAAa,CAAES,IAAI,CAACT,aAAa,CAAG,CACtC,CAAC,CAAC,CAAC,CAEH;AACA4B,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,eAAe,CAAE,CAAEC,MAAM,CAAE,CAAEL,OAAQ,CAAE,CAAC,CAAC,CAAC,CACjF,CAAC,IAAM,CACL;AACA7B,cAAc,CAAC4B,KAAK,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAO,cAAc,CAAIC,WAAW,EAAK,CACtC5C,WAAW,CAACoB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,GAAGwB,WAAY,CAAC,CAAC,CAAC,CACpD,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIR,OAAO,EAAK,CACrCjD,iBAAiB,CAACiD,OAAO,CAAC,CAC1B7B,cAAc,CAAC,EAAE,CAAC,CAElB;AACAE,WAAW,CAACU,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPT,aAAa,CAAES,IAAI,CAACT,aAAa,CAAG,CACtC,CAAC,CAAC,CAAC,CAEH;AACA9B,UAAU,CAACwD,OAAO,CAAE1D,aAAa,CAACgD,cAAc,CAACC,SAAS,CAAC,CAE3D;AACAW,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,eAAe,CAAE,CAAEC,MAAM,CAAE,CAAEL,OAAQ,CAAE,CAAC,CAAC,CAAC,CACjF,CAAC,CAED;AACApE,SAAS,CAAC,IAAM,CACd,GAAI8B,QAAQ,CAACI,OAAO,EAAIhB,cAAc,CAAE,CACtC,KAAM,CAAA2D,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjC;AACA,KAAM,CAAAnC,UAAU,CAAGoC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CAEpDxC,WAAW,CAACU,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPR,UAAU,CAAEQ,IAAI,CAACR,UAAU,CAAGA,UAChC,CAAC,CAAC,CAAC,CAEH;AACA,IAAK,GAAI,CAAAuC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGvC,UAAU,CAAEuC,CAAC,EAAE,CAAE,CACnC3E,SAAS,CAAC4E,OAAO,CAAC,mCAAmCC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIH,CAAC,EAAE,CAAC,CACzE,CACF,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMI,aAAa,CAACT,QAAQ,CAAC,CACtC,CACF,CAAC,CAAE,CAAC/C,QAAQ,CAACI,OAAO,CAAEhB,cAAc,CAAC,CAAC,CAEtC;AACAlB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6E,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCrC,WAAW,CAACU,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPP,SAAS,CAAEO,IAAI,CAACP,SAAS,CAAG,CAC9B,CAAC,CAAC,CAAC,CACL,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAM0C,aAAa,CAACT,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAU,oBAAoB,CAAGA,CAAA,GAAM,CACjC,mBACEvE,KAAA,QAAKwE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B3E,IAAA,CAACV,eAAe,EACdkC,WAAW,CAAEA,WAAY,CACzBoD,aAAa,CAAEd,iBAAkB,CACjC1D,cAAc,CAAEA,cAAe,CAC/BM,aAAa,CAAEA,aAAc,CAC7BE,WAAW,CAAEA,WAAY,CACzBE,gBAAgB,CAAEA,gBAAiB,CACpC,CAAC,CAGDpB,qBAAqB,CAACmF,QAAQ,CAACC,UAAU,eACxC9E,IAAA,QAAK0E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC3E,IAAA,CAAC+E,SAAS,GAAE,CAAC,CACV,CACN,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAA,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,EAAE,CAAEC,KAAK,CAAC,CAAGhG,QAAQ,CAAC,IAAI,CAAC,CAElCC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgG,SAAS,CAAGvF,aAAa,CAACwF,eAAe,CAAC,CAAC,CACjD,GAAID,SAAS,CAAE,CACbD,KAAK,CAACC,SAAS,CAACE,SAAS,CAAC,CAC5B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,GAAI,CAACJ,EAAE,CAAE,MAAO,KAAI,CAEpB,mBACEhF,IAAA,QACE0E,SAAS,CAAC,YAAY,CACtBW,uBAAuB,CAAE,CAAEC,MAAM,CAAEN,EAAG,CAAE,CACzC,CAAC,CAEN,CAAC,CAED;AACA,KAAM,CAAAO,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAACP,EAAE,CAAEC,KAAK,CAAC,CAAGhG,QAAQ,CAAC,IAAI,CAAC,CAElCC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgG,SAAS,CAAGvF,aAAa,CAAC6F,cAAc,CAAC,kBAAkB,CAAC,CAClE,GAAIN,SAAS,CAAE,CACbD,KAAK,CAACC,SAAS,CAACE,SAAS,CAAC,CAC5B,CACF,CAAC,CAAE,CAAChF,cAAc,CAAC,CAAC,CAAE;AAEtB,GAAI,CAAC4E,EAAE,EAAI,CAACtF,qBAAqB,CAACmF,QAAQ,CAACY,SAAS,CAAE,MAAO,KAAI,CAEjE,mBACEzF,IAAA,QACE0E,SAAS,CAAC,WAAW,CACrBW,uBAAuB,CAAE,CAAEC,MAAM,CAAEN,EAAG,CAAE,CACzC,CAAC,CAEN,CAAC,CAED,KAAM,CAAAU,UAAU,CAAIC,OAAO,EAAK,CAC9B,KAAM,CAAAC,KAAK,CAAG3B,IAAI,CAACC,KAAK,CAACyB,OAAO,CAAG,EAAE,CAAC,CACtC,KAAM,CAAAE,IAAI,CAAGF,OAAO,CAAG,EAAE,CACzB,GAAIC,KAAK,CAAG,CAAC,CAAE,CACb,MAAO,GAAGA,KAAK,KAAKC,IAAI,GAAG,CAC7B,CACA,MAAO,GAAGA,IAAI,GAAG,CACnB,CAAC,CAED,mBACE3F,KAAA,QAAKwE,SAAS,CAAE,OAAO1D,QAAQ,CAACK,SAAS,CAAG,YAAY,CAAG,aAAa,EAAG,CAAAsD,QAAA,eACzE3E,IAAA,CAACb,MAAM,EACL2G,WAAW,CAAE1C,eAAgB,CAC7B2C,eAAe,CAAEA,CAAA,GAAMxF,eAAe,CAAC,CAACD,YAAY,CAAE,CACtD0F,KAAK,CAAEtE,QAAS,CACjB,CAAC,cAGF1B,IAAA,CAACuF,QAAQ,GAAE,CAAC,cAEZrF,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,EACzBF,oBAAoB,CAAC,CAAC,cAEvBvE,KAAA,SAAMwE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5B3E,IAAA,CAACZ,qBAAqB,EACpBkE,OAAO,CAAElD,cAAe,CACxBY,QAAQ,CAAEA,QAAS,CACpB,CAAC,CAGDZ,cAAc,eACbF,KAAA,QAAKwE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzE,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,cACf3E,IAAA,SAAA2E,QAAA,CAAM,mDAAS,CAAM,CAAC,EACnB,CAAC,CAEL3D,QAAQ,CAACI,OAAO,eACflB,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,SAAA2E,QAAA,CAAM,oBAAG,CAAM,CAAC,cAChB3E,IAAA,SAAA2E,QAAA,CAAM,0GAAmB,CAAM,CAAC,EAC7B,CACN,cAEDzE,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,cACfzE,KAAA,SAAAyE,QAAA,EAAM,4BAAM,CAACnF,aAAa,CAACyG,eAAe,CAACjF,QAAQ,CAACE,OAAO,CAAC,EAAO,CAAC,EACjE,CAAC,cAENhB,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,cACfzE,KAAA,SAAAyE,QAAA,EAAOjD,QAAQ,CAACG,UAAU,CAAC,gEAAY,EAAM,CAAC,EAC3C,CAAC,cAEN3B,KAAA,QAAKwE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,cACfzE,KAAA,SAAAyE,QAAA,EAAM,uEAAc,CAACjF,qBAAqB,CAACwG,YAAY,CAAC,CAAC,CAACC,gBAAgB,EAAO,CAAC,EAC/E,CAAC,EACH,CACN,EACG,CAAC,CAEN7F,YAAY,eACXN,IAAA,CAACX,gBAAgB,EACf2B,QAAQ,CAAEA,QAAS,CACnBoF,gBAAgB,CAAExC,cAAe,CACjCyC,OAAO,CAAEA,CAAA,GAAM9F,eAAe,CAAC,KAAK,CAAE,CACtCyF,KAAK,CAAEtE,QAAS,CACjB,CACF,EACE,CAAC,CAGLA,QAAQ,CAACE,aAAa,GAAK,CAAC,EAAI,CAACxB,cAAc,EAAI,CAACU,gBAAgB,eACnEd,IAAA,QAAK0E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzE,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3E,IAAA,OAAA2E,QAAA,CAAI,uLAAoC,CAAI,CAAC,cAC7C3E,IAAA,MAAA2E,QAAA,CAAG,6JAA8B,CAAG,CAAC,cACrCzE,KAAA,OAAAyE,QAAA,eACE3E,IAAA,OAAA2E,QAAA,CAAI,yIAAyB,CAAI,CAAC,cAClC3E,IAAA,OAAA2E,QAAA,CAAI,2GAAoB,CAAI,CAAC,cAC7B3E,IAAA,OAAA2E,QAAA,CAAI,kHAAsB,CAAI,CAAC,cAC/B3E,IAAA,OAAA2E,QAAA,CAAI,kFAAe,CAAI,CAAC,cACxB3E,IAAA,OAAA2E,QAAA,CAAI,6JAA8B,CAAI,CAAC,EACrC,CAAC,cACLzE,KAAA,QAAKwE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B3E,IAAA,WACE0E,SAAS,CAAC,aAAa,CACvB4B,OAAO,CAAEA,CAAA,GAAM3E,WAAW,CAACU,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAET,aAAa,CAAE,CAAE,CAAC,CAAC,CAAE,CAAA+C,QAAA,CACrE,mDAED,CAAQ,CAAC,cACT3E,IAAA,WACE0E,SAAS,CAAC,eAAe,CACzB4B,OAAO,CAAEA,CAAA,GAAM7F,oBAAoB,CAAC,IAAI,CAAE,CAAAkE,QAAA,CAC3C,iFAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CACN,CAGA3D,QAAQ,CAACI,OAAO,EAAIhB,cAAc,eACjCJ,IAAA,QAAK0E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCzE,KAAA,SAAAyE,QAAA,EAAM,qDAAW,CAACjD,QAAQ,CAACG,UAAU,CAAC,iCAAM,EAAM,CAAC,CAChD,CACN,CAGArB,iBAAiB,eAChBR,IAAA,CAACT,aAAa,EAAC8G,OAAO,CAAEA,CAAA,GAAM5F,oBAAoB,CAAC,KAAK,CAAE,CAAE,CAC7D,cAGDT,IAAA,CAACT,aAAa,EAACgH,OAAO,CAAE,IAAK,CAAE,CAAC,EAC7B,CAAC,CAEV,CAEA,cAAe,CAAApG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}