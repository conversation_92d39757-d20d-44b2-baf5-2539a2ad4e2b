import React, { useState, useEffect } from 'react';

const Sidebar = ({ searchQuery, onVideoSelect }) => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);

  // فيديوهات مقترحة افتراضية
  const defaultVideos = [
    {
      id: 'dQw4w9WgXcQ',
      title: '<PERSON> - Never Gonna Give You Up',
      channel: '<PERSON>',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'
    },
    {
      id: 'kJQP7kiw5Fk',
      title: '<PERSON> ft. Daddy <PERSON>',
      channel: '<PERSON>',
      thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'
    },
    {
      id: 'fJ9rUzIMcZQ',
      title: 'Queen – Bohemian Rhapsody',
      channel: 'Queen Official',
      thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'
    },
    {
      id: 'JGwWNGJdvx8',
      title: 'Ed Sheeran - Shape of You',
      channel: 'Ed Sheeran',
      thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/mqdefault.jpg'
    },
    {
      id: 'RgKAFK5djSk',
      title: 'Wiz Khalifa - See You Again ft. Charlie Puth',
      channel: 'Wiz Khalifa',
      thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/mqdefault.jpg'
    }
  ];

  useEffect(() => {
    if (searchQuery) {
      performSearch(searchQuery);
    } else {
      setSearchResults(defaultVideos);
    }
  }, [searchQuery]);

  const performSearch = async (query) => {
    setLoading(true);
    try {
      // في التطبيق الحقيقي، ستحتاج إلى استخدام YouTube Data API
      // هنا سنستخدم بيانات وهمية للعرض التوضيحي
      
      // محاكاة تأخير البحث
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // نتائج بحث وهمية
      const mockResults = [
        {
          id: 'dQw4w9WgXcQ',
          title: `نتائج البحث عن: ${query}`,
          channel: 'قناة تجريبية',
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'
        },
        {
          id: 'kJQP7kiw5Fk',
          title: `فيديو متعلق بـ ${query}`,
          channel: 'قناة أخرى',
          thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'
        },
        {
          id: 'fJ9rUzIMcZQ',
          title: `محتوى حول ${query}`,
          channel: 'قناة المحتوى',
          thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'
        }
      ];
      
      setSearchResults(mockResults);
    } catch (error) {
      console.error('خطأ في البحث:', error);
      setSearchResults(defaultVideos);
    } finally {
      setLoading(false);
    }
  };

  const handleVideoClick = (videoId) => {
    onVideoSelect(videoId);
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3 className="sidebar-title">
          {searchQuery ? `نتائج البحث: ${searchQuery}` : 'فيديوهات مقترحة'}
        </h3>
      </div>
      
      <div className="sidebar-content">
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#b3b3b3' }}>
            <div>🔄</div>
            <p>جاري البحث...</p>
          </div>
        ) : (
          searchResults.map((video) => (
            <div 
              key={video.id}
              className="video-item"
              onClick={() => handleVideoClick(video.id)}
            >
              <img 
                src={video.thumbnail}
                alt={video.title}
                className="video-thumbnail"
                onError={(e) => {
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA4MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjM0QzRDNEIi8+CjxwYXRoIGQ9Ik0zMiAyMkw0OCAzMEwzMiAzOFYyMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+';
                }}
              />
              <div className="video-info">
                <div className="video-title">{video.title}</div>
                <div className="video-channel">{video.channel}</div>
              </div>
            </div>
          ))
        )}
        
        {!loading && searchResults.length === 0 && (
          <div style={{ textAlign: 'center', padding: '20px', color: '#b3b3b3' }}>
            <div>😔</div>
            <p>لم يتم العثور على نتائج</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;

