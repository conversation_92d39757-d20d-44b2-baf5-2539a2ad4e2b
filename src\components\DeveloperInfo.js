import React, { useState } from 'react';
import { channelConfig } from '../utils/channelConfig';

const DeveloperInfo = ({ onClose, compact = false }) => {
  const [showFullInfo, setShowFullInfo] = useState(!compact);
  const { developer } = channelConfig;

  const handleSocialClick = (url, platform) => {
    window.open(url, '_blank');
    
    // تسجيل النقرة لإحصائيات المطور
    const clickData = {
      platform,
      url,
      timestamp: new Date().toISOString()
    };
    
    const existingClicks = JSON.parse(localStorage.getItem('social-clicks') || '[]');
    existingClicks.push(clickData);
    localStorage.setItem('social-clicks', JSON.stringify(existingClicks));
  };

  if (compact) {
    return (
      <div className="developer-info-compact">
        <div className="developer-credit">
          <span className="credit-text">تم بواسطة: {developer.name}</span>
          <button 
            className="info-toggle-btn"
            onClick={() => setShowFullInfo(true)}
            title="معلومات المطور"
          >
            ℹ️
          </button>
        </div>
        
        {showFullInfo && (
          <DeveloperInfo onClose={() => setShowFullInfo(false)} compact={false} />
        )}
      </div>
    );
  }

  return (
    <div className="developer-overlay" onClick={onClose}>
      <div className="developer-info-modal" onClick={(e) => e.stopPropagation()}>
        <div className="developer-header">
          <div className="developer-avatar">
            <div className="avatar-placeholder">
              {developer.name.split(' ').map(n => n[0]).join('')}
            </div>
          </div>
          <div className="developer-details">
            <h2>{developer.name}</h2>
            <p className="developer-title">مطور تطبيقات ومهندس برمجيات</p>
            <p className="developer-channel">{developer.channelName}</p>
          </div>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="developer-content">
          <div className="contact-section">
            <h3>📞 معلومات التواصل</h3>
            <div className="contact-item">
              <span className="contact-icon">📱</span>
              <span className="contact-text">{developer.phone}</span>
              <button 
                className="contact-btn"
                onClick={() => window.open(`tel:${developer.phone}`)}
              >
                اتصال
              </button>
            </div>
          </div>

          <div className="social-section">
            <h3>🌐 روابط التواصل الاجتماعي</h3>
            
            <div className="social-links">
              <div 
                className="social-item youtube"
                onClick={() => handleSocialClick(developer.socialLinks.youtube, 'youtube')}
              >
                <div className="social-icon">🎥</div>
                <div className="social-info">
                  <div className="social-name">قناة يوتيوب</div>
                  <div className="social-handle">@essa8020</div>
                </div>
                <div className="social-arrow">→</div>
              </div>

              <div 
                className="social-item facebook"
                onClick={() => handleSocialClick(developer.socialLinks.facebook, 'facebook')}
              >
                <div className="social-icon">📘</div>
                <div className="social-info">
                  <div className="social-name">فيسبوك</div>
                  <div className="social-handle">الملف الشخصي</div>
                </div>
                <div className="social-arrow">→</div>
              </div>

              <div 
                className="social-item telegram"
                onClick={() => handleSocialClick(developer.socialLinks.telegram, 'telegram')}
              >
                <div className="social-icon">✈️</div>
                <div className="social-info">
                  <div className="social-name">تليجرام</div>
                  <div className="social-handle">قناة دارك سايبر اكس</div>
                </div>
                <div className="social-arrow">→</div>
              </div>
            </div>
          </div>

          <div className="about-section">
            <h3>👨‍💻 نبذة عن المطور</h3>
            <p>
              مهندس برمجيات متخصص في تطوير التطبيقات والمواقع الإلكترونية. 
              صاحب قناة "{developer.channelName}" التقنية التي تقدم محتوى تعليمي 
              في مجال البرمجة والتطوير والأمن السيبراني.
            </p>
            
            <div className="skills-tags">
              <span className="skill-tag">React</span>
              <span className="skill-tag">Electron</span>
              <span className="skill-tag">JavaScript</span>
              <span className="skill-tag">Node.js</span>
              <span className="skill-tag">Python</span>
              <span className="skill-tag">Cybersecurity</span>
            </div>
          </div>

          <div className="app-info-section">
            <h3>📱 حول هذا التطبيق</h3>
            <p>
              مشغل يوتيوب متقدم مع واجهة عربية، مانع إعلانات قوي، وإعدادات متقدمة 
              لتجربة مشاهدة محسنة. تم تطويره خصيصاً لتوفير تجربة مشاهدة خالية من 
              الإعلانات مع دعم كامل للغة العربية.
            </p>
            
            <div className="app-features">
              <div className="feature-item">✅ مانع إعلانات قوي</div>
              <div className="feature-item">✅ واجهة عربية كاملة</div>
              <div className="feature-item">✅ ثيم داكن مريح</div>
              <div className="feature-item">✅ جودة فيديو متقدمة</div>
            </div>
          </div>
        </div>

        <div className="developer-footer">
          <p>شكراً لاستخدام التطبيق! 🙏</p>
          <p>لا تنس الاشتراك في القناة ومتابعة المحتوى الجديد</p>
        </div>
      </div>
    </div>
  );
};

export default DeveloperInfo;

