{"ast": null, "code": "// إعدادات القناة الافتراضية ومعلومات المطور\nexport const channelConfig={// معلومات القناة الافتراضية\ndefaultChannel:{url:'https://www.youtube.com/@essa8020',channelId:'UCessa8020',// سيتم استخراجه تلقائياً\nname:'دارك سايبر اكس',description:'قناة تقنية متخصصة في البرمجة والتطوير'},// معلومات المطور\ndeveloper:{name:'المهندس محمد عيسى',phone:'01009046911',channelName:'دارك سايبر اكس',socialLinks:{youtube:'https://www.youtube.com/@essa8020',facebook:'https://www.facebook.com/profile.php?id=61578756761846',telegram:'https://t.me/+iPn4N3EoWwAzNDE0'}},// فيديوهات افتراضية للعرض (محاكاة)\ndefaultVideos:[{id:'default1',title:'مرحباً بك في قناة دارك سايبر اكس',description:'تعلم البرمجة والتطوير مع أحدث التقنيات',thumbnail:'/assets/default-video-1.jpg',duration:'10:30',views:'1.2K',uploadDate:'2024-01-15'},{id:'default2',title:'تطوير التطبيقات باستخدام React و Electron',description:'دورة شاملة لتطوير تطبيقات سطح المكتب',thumbnail:'/assets/default-video-2.jpg',duration:'25:45',views:'3.5K',uploadDate:'2024-01-10'},{id:'default3',title:'أساسيات الأمن السيبراني للمطورين',description:'كيفية حماية تطبيقاتك من الثغرات الأمنية',thumbnail:'/assets/default-video-3.jpg',duration:'18:20',views:'2.8K',uploadDate:'2024-01-05'},{id:'default4',title:'بناء مشغل يوتيوب مخصص',description:'تطوير مشغل فيديو متقدم بواجهة عربية',thumbnail:'/assets/default-video-4.jpg',duration:'32:15',views:'5.1K',uploadDate:'2024-01-01'},{id:'default5',title:'تقنيات الذكاء الاصطناعي في التطوير',description:'استخدام AI لتحسين تطبيقاتك',thumbnail:'/assets/default-video-5.jpg',duration:'22:40',views:'4.2K',uploadDate:'2023-12-28'}]};// دالة لاستخراج معرف القناة من الرابط\nexport const extractChannelId=channelUrl=>{try{const url=new URL(channelUrl);const pathname=url.pathname;// استخراج معرف القناة من أنواع روابط مختلفة\nif(pathname.includes('/@')){return pathname.split('/@')[1];}else if(pathname.includes('/channel/')){return pathname.split('/channel/')[1];}else if(pathname.includes('/c/')){return pathname.split('/c/')[1];}return'essa8020';// افتراضي\n}catch(error){console.error('Error extracting channel ID:',error);return'essa8020';}};// دالة لتحميل فيديوهات القناة (محاكاة)\nexport const loadChannelVideos=async channelId=>{// في التطبيق الحقيقي، ستقوم باستدعاء YouTube API\n// هنا سنعيد الفيديوهات الافتراضية مع تأخير لمحاكاة التحميل\nreturn new Promise(resolve=>{setTimeout(()=>{resolve({channelInfo:{name:channelConfig.developer.channelName,subscribers:'12.5K',description:'قناة تقنية متخصصة في البرمجة والتطوير والأمن السيبراني',avatar:'/assets/channel-avatar.jpg'},videos:channelConfig.defaultVideos.map(video=>({...video,channelName:channelConfig.developer.channelName,channelId:channelId}))});},1000);});};// دالة لتسجيل مشاهدة (لإحصائيات المطور)\nexport const recordView=(videoId,channelId)=>{// تسجيل المشاهدة في الإحصائيات المحلية\nconst viewData={videoId,channelId,timestamp:new Date().toISOString(),userAgent:navigator.userAgent};// حفظ في التخزين المحلي\nconst existingViews=JSON.parse(localStorage.getItem('channel-views')||'[]');existingViews.push(viewData);localStorage.setItem('channel-views',JSON.stringify(existingViews));// في التطبيق الحقيقي، يمكن إرسال البيانات لخادم التحليلات\nconsole.log('View recorded:',viewData);};export default channelConfig;", "map": {"version": 3, "names": ["channelConfig", "defaultChannel", "url", "channelId", "name", "description", "developer", "phone", "channelName", "socialLinks", "youtube", "facebook", "telegram", "defaultVideos", "id", "title", "thumbnail", "duration", "views", "uploadDate", "extractChannelId", "channelUrl", "URL", "pathname", "includes", "split", "error", "console", "loadChannelVideos", "Promise", "resolve", "setTimeout", "channelInfo", "subscribers", "avatar", "videos", "map", "video", "recordView", "videoId", "viewData", "timestamp", "Date", "toISOString", "userAgent", "navigator", "existingViews", "JSON", "parse", "localStorage", "getItem", "push", "setItem", "stringify", "log"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/utils/channelConfig.js"], "sourcesContent": ["// إعدادات القناة الافتراضية ومعلومات المطور\nexport const channelConfig = {\n  // معلومات القناة الافتراضية\n  defaultChannel: {\n    url: 'https://www.youtube.com/@essa8020',\n    channelId: 'UCessa8020', // سيتم استخراجه تلقائياً\n    name: 'دارك سايبر اكس',\n    description: 'قناة تقنية متخصصة في البرمجة والتطوير'\n  },\n\n  // معلومات المطور\n  developer: {\n    name: 'المهندس محمد عيسى',\n    phone: '01009046911',\n    channelName: 'دارك سايبر اكس',\n    socialLinks: {\n      youtube: 'https://www.youtube.com/@essa8020',\n      facebook: 'https://www.facebook.com/profile.php?id=61578756761846',\n      telegram: 'https://t.me/+iPn4N3EoWwAzNDE0'\n    }\n  },\n\n  // فيديوهات افتراضية للعرض (محاكاة)\n  defaultVideos: [\n    {\n      id: 'default1',\n      title: 'مرحباً بك في قناة دارك سايبر اكس',\n      description: 'تعلم البرمجة والتطوير مع أحدث التقنيات',\n      thumbnail: '/assets/default-video-1.jpg',\n      duration: '10:30',\n      views: '1.2K',\n      uploadDate: '2024-01-15'\n    },\n    {\n      id: 'default2', \n      title: 'تطوير التطبيقات باستخدام React و Electron',\n      description: 'دورة شاملة لتطوير تطبيقات سطح المكتب',\n      thumbnail: '/assets/default-video-2.jpg',\n      duration: '25:45',\n      views: '3.5K',\n      uploadDate: '2024-01-10'\n    },\n    {\n      id: 'default3',\n      title: 'أساسيات الأمن السيبراني للمطورين',\n      description: 'كيفية حماية تطبيقاتك من الثغرات الأمنية',\n      thumbnail: '/assets/default-video-3.jpg',\n      duration: '18:20',\n      views: '2.8K',\n      uploadDate: '2024-01-05'\n    },\n    {\n      id: 'default4',\n      title: 'بناء مشغل يوتيوب مخصص',\n      description: 'تطوير مشغل فيديو متقدم بواجهة عربية',\n      thumbnail: '/assets/default-video-4.jpg',\n      duration: '32:15',\n      views: '5.1K',\n      uploadDate: '2024-01-01'\n    },\n    {\n      id: 'default5',\n      title: 'تقنيات الذكاء الاصطناعي في التطوير',\n      description: 'استخدام AI لتحسين تطبيقاتك',\n      thumbnail: '/assets/default-video-5.jpg',\n      duration: '22:40',\n      views: '4.2K',\n      uploadDate: '2023-12-28'\n    }\n  ]\n};\n\n// دالة لاستخراج معرف القناة من الرابط\nexport const extractChannelId = (channelUrl) => {\n  try {\n    const url = new URL(channelUrl);\n    const pathname = url.pathname;\n    \n    // استخراج معرف القناة من أنواع روابط مختلفة\n    if (pathname.includes('/@')) {\n      return pathname.split('/@')[1];\n    } else if (pathname.includes('/channel/')) {\n      return pathname.split('/channel/')[1];\n    } else if (pathname.includes('/c/')) {\n      return pathname.split('/c/')[1];\n    }\n    \n    return 'essa8020'; // افتراضي\n  } catch (error) {\n    console.error('Error extracting channel ID:', error);\n    return 'essa8020';\n  }\n};\n\n// دالة لتحميل فيديوهات القناة (محاكاة)\nexport const loadChannelVideos = async (channelId) => {\n  // في التطبيق الحقيقي، ستقوم باستدعاء YouTube API\n  // هنا سنعيد الفيديوهات الافتراضية مع تأخير لمحاكاة التحميل\n  \n  return new Promise((resolve) => {\n    setTimeout(() => {\n      resolve({\n        channelInfo: {\n          name: channelConfig.developer.channelName,\n          subscribers: '12.5K',\n          description: 'قناة تقنية متخصصة في البرمجة والتطوير والأمن السيبراني',\n          avatar: '/assets/channel-avatar.jpg'\n        },\n        videos: channelConfig.defaultVideos.map(video => ({\n          ...video,\n          channelName: channelConfig.developer.channelName,\n          channelId: channelId\n        }))\n      });\n    }, 1000);\n  });\n};\n\n// دالة لتسجيل مشاهدة (لإحصائيات المطور)\nexport const recordView = (videoId, channelId) => {\n  // تسجيل المشاهدة في الإحصائيات المحلية\n  const viewData = {\n    videoId,\n    channelId,\n    timestamp: new Date().toISOString(),\n    userAgent: navigator.userAgent\n  };\n  \n  // حفظ في التخزين المحلي\n  const existingViews = JSON.parse(localStorage.getItem('channel-views') || '[]');\n  existingViews.push(viewData);\n  localStorage.setItem('channel-views', JSON.stringify(existingViews));\n  \n  // في التطبيق الحقيقي، يمكن إرسال البيانات لخادم التحليلات\n  console.log('View recorded:', viewData);\n};\n\nexport default channelConfig;\n\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,aAAa,CAAG,CAC3B;AACAC,cAAc,CAAE,CACdC,GAAG,CAAE,mCAAmC,CACxCC,SAAS,CAAE,YAAY,CAAE;AACzBC,IAAI,CAAE,gBAAgB,CACtBC,WAAW,CAAE,uCACf,CAAC,CAED;AACAC,SAAS,CAAE,CACTF,IAAI,CAAE,mBAAmB,CACzBG,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,gBAAgB,CAC7BC,WAAW,CAAE,CACXC,OAAO,CAAE,mCAAmC,CAC5CC,QAAQ,CAAE,wDAAwD,CAClEC,QAAQ,CAAE,gCACZ,CACF,CAAC,CAED;AACAC,aAAa,CAAE,CACb,CACEC,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,kCAAkC,CACzCV,WAAW,CAAE,wCAAwC,CACrDW,SAAS,CAAE,6BAA6B,CACxCC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,YACd,CAAC,CACD,CACEL,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,2CAA2C,CAClDV,WAAW,CAAE,sCAAsC,CACnDW,SAAS,CAAE,6BAA6B,CACxCC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,YACd,CAAC,CACD,CACEL,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,kCAAkC,CACzCV,WAAW,CAAE,yCAAyC,CACtDW,SAAS,CAAE,6BAA6B,CACxCC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,YACd,CAAC,CACD,CACEL,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,uBAAuB,CAC9BV,WAAW,CAAE,qCAAqC,CAClDW,SAAS,CAAE,6BAA6B,CACxCC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,YACd,CAAC,CACD,CACEL,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,oCAAoC,CAC3CV,WAAW,CAAE,4BAA4B,CACzCW,SAAS,CAAE,6BAA6B,CACxCC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,YACd,CAAC,CAEL,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,gBAAgB,CAAIC,UAAU,EAAK,CAC9C,GAAI,CACF,KAAM,CAAAnB,GAAG,CAAG,GAAI,CAAAoB,GAAG,CAACD,UAAU,CAAC,CAC/B,KAAM,CAAAE,QAAQ,CAAGrB,GAAG,CAACqB,QAAQ,CAE7B;AACA,GAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAE,CAC3B,MAAO,CAAAD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAChC,CAAC,IAAM,IAAIF,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAE,CACzC,MAAO,CAAAD,QAAQ,CAACE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CACvC,CAAC,IAAM,IAAIF,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACnC,MAAO,CAAAD,QAAQ,CAACE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACjC,CAEA,MAAO,UAAU,CAAE;AACrB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,UAAU,CACnB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,iBAAiB,CAAG,KAAO,CAAAzB,SAAS,EAAK,CACpD;AACA;AAEA,MAAO,IAAI,CAAA0B,OAAO,CAAEC,OAAO,EAAK,CAC9BC,UAAU,CAAC,IAAM,CACfD,OAAO,CAAC,CACNE,WAAW,CAAE,CACX5B,IAAI,CAAEJ,aAAa,CAACM,SAAS,CAACE,WAAW,CACzCyB,WAAW,CAAE,OAAO,CACpB5B,WAAW,CAAE,wDAAwD,CACrE6B,MAAM,CAAE,4BACV,CAAC,CACDC,MAAM,CAAEnC,aAAa,CAACa,aAAa,CAACuB,GAAG,CAACC,KAAK,GAAK,CAChD,GAAGA,KAAK,CACR7B,WAAW,CAAER,aAAa,CAACM,SAAS,CAACE,WAAW,CAChDL,SAAS,CAAEA,SACb,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAC,CACJ,CAAC,CAED;AACA,MAAO,MAAM,CAAAmC,UAAU,CAAGA,CAACC,OAAO,CAAEpC,SAAS,GAAK,CAChD;AACA,KAAM,CAAAqC,QAAQ,CAAG,CACfD,OAAO,CACPpC,SAAS,CACTsC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAEC,SAAS,CAACD,SACvB,CAAC,CAED;AACA,KAAM,CAAAE,aAAa,CAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,EAAI,IAAI,CAAC,CAC/EJ,aAAa,CAACK,IAAI,CAACX,QAAQ,CAAC,CAC5BS,YAAY,CAACG,OAAO,CAAC,eAAe,CAAEL,IAAI,CAACM,SAAS,CAACP,aAAa,CAAC,CAAC,CAEpE;AACAnB,OAAO,CAAC2B,GAAG,CAAC,gBAAgB,CAAEd,QAAQ,CAAC,CACzC,CAAC,CAED,cAAe,CAAAxC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}