// مساعدات YouTube API
export class YouTubeHelper {
  static extractVideoId(url) {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  static generateThumbnailUrl(videoId, quality = 'mqdefault') {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
  }

  static formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  static getQualityLabel(quality) {
    const qualityMap = {
      'auto': 'تلقائي',
      'hd2160': '4K (2160p)',
      'hd1440': '1440p',
      'hd1080': '1080p',
      'hd720': '720p',
      'large': '480p',
      'medium': '360p',
      'small': '240p'
    };
    return qualityMap[quality] || quality;
  }

  // محاكاة بحث YouTube (في التطبيق الحقيقي ستحتاج YouTube Data API)
  static async searchVideos(query, maxResults = 10) {
    // بيانات وهمية للعرض التوضيحي
    const mockVideos = [
      {
        id: 'dQw4w9WgXcQ',
        title: `${query} - فيديو تجريبي 1`,
        channel: 'قناة تجريبية',
        duration: '3:32',
        views: '1.2M',
        publishedAt: '2023-01-15'
      },
      {
        id: 'kJQP7kiw5Fk',
        title: `محتوى حول ${query}`,
        channel: 'قناة المحتوى',
        duration: '4:42',
        views: '8.5M',
        publishedAt: '2023-02-20'
      },
      {
        id: 'fJ9rUzIMcZQ',
        title: `شرح ${query} بالتفصيل`,
        channel: 'قناة التعليم',
        duration: '5:55',
        views: '3.1M',
        publishedAt: '2023-03-10'
      },
      {
        id: 'JGwWNGJdvx8',
        title: `أفضل ${query} لهذا العام`,
        channel: 'قناة المراجعات',
        duration: '2:18',
        views: '950K',
        publishedAt: '2023-04-05'
      },
      {
        id: 'RgKAFK5djSk',
        title: `كيفية استخدام ${query}`,
        channel: 'قناة الشروحات',
        duration: '6:12',
        views: '2.7M',
        publishedAt: '2023-05-12'
      }
    ];

    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 800));

    return mockVideos.slice(0, maxResults).map(video => ({
      ...video,
      thumbnail: this.generateThumbnailUrl(video.id)
    }));
  }

  // الحصول على معلومات الفيديو
  static async getVideoInfo(videoId) {
    // بيانات وهمية للعرض التوضيحي
    const mockInfo = {
      id: videoId,
      title: 'عنوان الفيديو',
      channel: 'اسم القناة',
      description: 'وصف الفيديو...',
      duration: '3:45',
      views: '1.5M',
      likes: '45K',
      publishedAt: '2023-06-01',
      thumbnail: this.generateThumbnailUrl(videoId)
    };

    await new Promise(resolve => setTimeout(resolve, 300));
    return mockInfo;
  }
}

// إعدادات مانع الإعلانات
export const adBlockFilters = [
  // إعلانات Google/YouTube
  '*://*.doubleclick.net/*',
  '*://*.googleadservices.com/*',
  '*://*.googlesyndication.com/*',
  '*://googleads.g.doubleclick.net/*',
  
  // إعلانات YouTube المحددة
  '*://*.youtube.com/api/stats/ads*',
  '*://*.youtube.com/ptracking*',
  '*://*.youtube.com/pagead/*',
  '*://www.youtube.com/api/stats/ads*',
  '*://www.youtube.com/ptracking*',
  '*://www.youtube.com/pagead/*',
  
  // إعلانات أخرى شائعة
  '*://*.adsystem.com/*',
  '*://*.amazon-adsystem.com/*',
  '*://*.facebook.com/tr*',
  '*://*.google-analytics.com/*'
];

// إعدادات الجودة المتاحة
export const qualityOptions = [
  { value: 'auto', label: 'تلقائي', description: 'أفضل جودة متاحة' },
  { value: 'hd2160', label: '4K (2160p)', description: 'جودة فائقة' },
  { value: 'hd1440', label: '1440p', description: 'جودة عالية جداً' },
  { value: 'hd1080', label: '1080p', description: 'جودة عالية' },
  { value: 'hd720', label: '720p', description: 'جودة متوسطة عالية' },
  { value: 'large', label: '480p', description: 'جودة متوسطة' },
  { value: 'medium', label: '360p', description: 'جودة منخفضة' },
  { value: 'small', label: '240p', description: 'جودة منخفضة جداً' }
];

