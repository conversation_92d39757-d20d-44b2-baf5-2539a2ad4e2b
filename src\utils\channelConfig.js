// إعدادات القناة الافتراضية ومعلومات المطور
export const channelConfig = {
  // معلومات القناة الافتراضية
  defaultChannel: {
    url: 'https://www.youtube.com/@essa8020',
    channelId: 'UCessa8020', // سيتم استخراجه تلقائياً
    name: 'دارك سايبر اكس',
    description: 'قناة تقنية متخصصة في البرمجة والتطوير'
  },

  // معلومات المطور
  developer: {
    name: 'المهندس محمد عيسى',
    phone: '01009046911',
    channelName: 'دارك سايبر اكس',
    socialLinks: {
      youtube: 'https://www.youtube.com/@essa8020',
      facebook: 'https://www.facebook.com/profile.php?id=61578756761846',
      telegram: 'https://t.me/+iPn4N3EoWwAzNDE0'
    }
  },

  // فيديوهات افتراضية للعرض (محاكاة)
  defaultVideos: [
    {
      id: 'default1',
      title: 'مرحباً بك في قناة دارك سايبر اكس',
      description: 'تعلم البرمجة والتطوير مع أحدث التقنيات',
      thumbnail: '/assets/default-video-1.jpg',
      duration: '10:30',
      views: '1.2K',
      uploadDate: '2024-01-15'
    },
    {
      id: 'default2', 
      title: 'تطوير التطبيقات باستخدام React و Electron',
      description: 'دورة شاملة لتطوير تطبيقات سطح المكتب',
      thumbnail: '/assets/default-video-2.jpg',
      duration: '25:45',
      views: '3.5K',
      uploadDate: '2024-01-10'
    },
    {
      id: 'default3',
      title: 'أساسيات الأمن السيبراني للمطورين',
      description: 'كيفية حماية تطبيقاتك من الثغرات الأمنية',
      thumbnail: '/assets/default-video-3.jpg',
      duration: '18:20',
      views: '2.8K',
      uploadDate: '2024-01-05'
    },
    {
      id: 'default4',
      title: 'بناء مشغل يوتيوب مخصص',
      description: 'تطوير مشغل فيديو متقدم بواجهة عربية',
      thumbnail: '/assets/default-video-4.jpg',
      duration: '32:15',
      views: '5.1K',
      uploadDate: '2024-01-01'
    },
    {
      id: 'default5',
      title: 'تقنيات الذكاء الاصطناعي في التطوير',
      description: 'استخدام AI لتحسين تطبيقاتك',
      thumbnail: '/assets/default-video-5.jpg',
      duration: '22:40',
      views: '4.2K',
      uploadDate: '2023-12-28'
    }
  ]
};

// دالة لاستخراج معرف القناة من الرابط
export const extractChannelId = (channelUrl) => {
  try {
    const url = new URL(channelUrl);
    const pathname = url.pathname;
    
    // استخراج معرف القناة من أنواع روابط مختلفة
    if (pathname.includes('/@')) {
      return pathname.split('/@')[1];
    } else if (pathname.includes('/channel/')) {
      return pathname.split('/channel/')[1];
    } else if (pathname.includes('/c/')) {
      return pathname.split('/c/')[1];
    }
    
    return 'essa8020'; // افتراضي
  } catch (error) {
    console.error('Error extracting channel ID:', error);
    return 'essa8020';
  }
};

// دالة لتحميل فيديوهات القناة (محاكاة)
export const loadChannelVideos = async (channelId) => {
  // في التطبيق الحقيقي، ستقوم باستدعاء YouTube API
  // هنا سنعيد الفيديوهات الافتراضية مع تأخير لمحاكاة التحميل
  
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        channelInfo: {
          name: channelConfig.developer.channelName,
          subscribers: '12.5K',
          description: 'قناة تقنية متخصصة في البرمجة والتطوير والأمن السيبراني',
          avatar: '/assets/channel-avatar.jpg'
        },
        videos: channelConfig.defaultVideos.map(video => ({
          ...video,
          channelName: channelConfig.developer.channelName,
          channelId: channelId
        }))
      });
    }, 1000);
  });
};

// دالة لتسجيل مشاهدة (لإحصائيات المطور)
export const recordView = (videoId, channelId) => {
  // تسجيل المشاهدة في الإحصائيات المحلية
  const viewData = {
    videoId,
    channelId,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent
  };
  
  // حفظ في التخزين المحلي
  const existingViews = JSON.parse(localStorage.getItem('channel-views') || '[]');
  existingViews.push(viewData);
  localStorage.setItem('channel-views', JSON.stringify(existingViews));
  
  // في التطبيق الحقيقي، يمكن إرسال البيانات لخادم التحليلات
  console.log('View recorded:', viewData);
};

export default channelConfig;

