{"ast": null, "code": "import React,{useState}from'react';import DeveloperPanel from'./DeveloperPanel';import{adBlocker}from'../utils/adBlocker';import{developerMonetization}from'../utils/developerMonetization';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SettingsEnhanced=_ref=>{let{settings,onSettingsChange,onClose,stats}=_ref;const[showDeveloperPanel,setShowDeveloperPanel]=useState(false);const[developerMode,setDeveloperMode]=useState(false);const[developerPassword,setDeveloperPassword]=useState('');const handleQualityChange=e=>{onSettingsChange({quality:e.target.value});};const handleToggle=setting=>{onSettingsChange({[setting]:!settings[setting]});};const handleDeveloperAccess=()=>{// كلمة مرور بسيطة للوصول للوحة المطور\nconst correctPassword='dev2024';// يمكنك تغييرها\nif(developerPassword===correctPassword){setDeveloperMode(true);setShowDeveloperPanel(true);setDeveloperPassword('');}else{alert('كلمة المرور غير صحيحة');setDeveloperPassword('');}};const qualityOptions=[{value:'auto',label:'تلقائي'},{value:'hd2160',label:'4K (2160p)'},{value:'hd1440',label:'1440p'},{value:'hd1080',label:'1080p'},{value:'hd720',label:'720p'},{value:'large',label:'480p'},{value:'medium',label:'360p'},{value:'small',label:'240p'}];const adStats=adBlocker.getStats();const monetizationStats=developerMonetization.getAnalytics();return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"settings-overlay\",onClick:onClose,children:/*#__PURE__*/_jsxs(\"div\",{className:\"settings-modal\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"settings-header\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"settings-title\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:onClose,children:\"\\u2715\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u062C\\u0648\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0627\\u0644\\u0627\\u0641\\u062A\\u0631\\u0627\\u0636\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"div\",{className:\"setting-control\",children:/*#__PURE__*/_jsx(\"select\",{value:settings.quality,onChange:handleQualityChange,children:qualityOptions.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0625\\u062E\\u0641\\u0627\\u0621 \\u0639\\u0646\\u0627\\u0635\\u0631 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${settings.hideControls?'active':''}`,onClick:()=>handleToggle('hideControls')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0645\\u0633\\u062A\\u0648\\u0649 \\u0627\\u0644\\u0635\\u0648\\u062A\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-control\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"0\",max:\"100\",value:settings.volume||100,onChange:e=>onSettingsChange({volume:parseInt(e.target.value)}),className:\"volume-slider\"}),/*#__PURE__*/_jsxs(\"span\",{children:[settings.volume||100,\"%\"]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0645\\u0627\\u0646\\u0639 \\u0627\\u0644\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${settings.adBlock?'active':''}`,onClick:()=>handleToggle('adBlock')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0627\\u0644\\u062B\\u064A\\u0645 \\u0627\\u0644\\u062F\\u0627\\u0643\\u0646\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${settings.darkTheme?'active':''}`,onClick:()=>handleToggle('darkTheme')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0627\\u0644\\u062A\\u0634\\u063A\\u064A\\u0644 \\u0627\\u0644\\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\"}),/*#__PURE__*/_jsx(\"div\",{className:`toggle-switch ${settings.autoplay?'active':''}`,onClick:()=>handleToggle('autoplay')})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stats-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:(stats===null||stats===void 0?void 0:stats.videosWatched)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0645\\u0634\\u0627\\u0647\\u062F\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:adStats.totalBlocked}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0625\\u0639\\u0644\\u0627\\u0646\\u0627\\u062A \\u0645\\u062D\\u062C\\u0648\\u0628\\u0629\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-value\",children:[(stats===null||stats===void 0?void 0:stats.timeSpent)||0,\"\\u062F\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0648\\u0642\\u062A \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-value\",children:monetizationStats.impressions}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-label\",children:\"\\u0645\\u0631\\u0627\\u062A \\u0627\\u0644\\u0638\\u0647\\u0648\\u0631\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631\"}),/*#__PURE__*/_jsx(\"span\",{children:\"1.0.0\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\"}),/*#__PURE__*/_jsx(\"span\",{children:\"YouTube Player Developer\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B\"}),/*#__PURE__*/_jsx(\"span\",{children:new Date().toLocaleDateString('ar-SA')})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"}),!developerMode?/*#__PURE__*/_jsx(\"div\",{className:\"developer-access\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-label\",children:\"\\u0648\\u0636\\u0639 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"developer-login\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"password\",placeholder:\"\\u0643\\u0644\\u0645\\u0629 \\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\",value:developerPassword,onChange:e=>setDeveloperPassword(e.target.value),onKeyPress:e=>e.key==='Enter'&&handleDeveloperAccess()}),/*#__PURE__*/_jsx(\"button\",{onClick:handleDeveloperAccess,children:\"\\u062F\\u062E\\u0648\\u0644\"})]})]})}):/*#__PURE__*/_jsxs(\"div\",{className:\"developer-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"developer-panel-btn\",onClick:()=>setShowDeveloperPanel(true),children:\"\\uD83D\\uDCBC \\u0641\\u062A\\u062D \\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0645\\u0637\\u0648\\u0631\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"developer-stats\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\uD83C\\uDFAF \\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u0646\\u0642\\u0631: \",monetizationStats.ctr]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\uD83D\\uDCB0 \\u0627\\u0644\\u0623\\u0631\\u0628\\u0627\\u062D: \",monetizationStats.revenueFormatted]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\uD83D\\uDCCA \\u0627\\u0644\\u062A\\u062D\\u0648\\u064A\\u0644\\u0627\\u062A: \",monetizationStats.conversions]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',marginTop:'20px',color:'#b3b3b3',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u0645\\u0634\\u063A\\u0644 \\u064A\\u0648\\u062A\\u064A\\u0648\\u0628 \\u0645\\u062E\\u0635\\u0635 \\u0645\\u0639 \\u0648\\u0627\\u062C\\u0647\\u0629 \\u0639\\u0631\\u0628\\u064A\\u0629\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0642 \\u0645\\u062D\\u0641\\u0648\\u0638\\u0629 \\xA9 2024\"})]})]})}),showDeveloperPanel&&/*#__PURE__*/_jsx(DeveloperPanel,{onClose:()=>setShowDeveloperPanel(false)})]});};export default SettingsEnhanced;", "map": {"version": 3, "names": ["React", "useState", "DeveloperPanel", "ad<PERSON><PERSON><PERSON>", "developerMonetization", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SettingsEnhanced", "_ref", "settings", "onSettingsChange", "onClose", "stats", "showDeveloperPanel", "setShowDeveloperPanel", "developerMode", "setDeveloperMode", "developerPassword", "setDeveloperPassword", "handleQualityChange", "e", "quality", "target", "value", "handleToggle", "setting", "handleDeveloperAccess", "correctPassword", "alert", "qualityOptions", "label", "adStats", "getStats", "monetizationStats", "getAnalytics", "children", "className", "onClick", "stopPropagation", "onChange", "map", "option", "hideControls", "type", "min", "max", "volume", "parseInt", "adBlock", "darkTheme", "autoplay", "videosWatched", "totalBlocked", "timeSpent", "impressions", "Date", "toLocaleDateString", "placeholder", "onKeyPress", "key", "ctr", "revenueFormatted", "conversions", "style", "textAlign", "marginTop", "color", "fontSize"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/components/SettingsEnhanced.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport DeveloperPanel from './DeveloperPanel';\nimport { adBlocker } from '../utils/adBlocker';\nimport { developerMonetization } from '../utils/developerMonetization';\n\nconst SettingsEnhanced = ({ settings, onSettingsChange, onClose, stats }) => {\n  const [showDeveloperPanel, setShowDeveloperPanel] = useState(false);\n  const [developerMode, setDeveloperMode] = useState(false);\n  const [developerPassword, setDeveloperPassword] = useState('');\n\n  const handleQualityChange = (e) => {\n    onSettingsChange({ quality: e.target.value });\n  };\n\n  const handleToggle = (setting) => {\n    onSettingsChange({ [setting]: !settings[setting] });\n  };\n\n  const handleDeveloperAccess = () => {\n    // كلمة مرور بسيطة للوصول للوحة المطور\n    const correctPassword = 'dev2024'; // يمكنك تغييرها\n    \n    if (developerPassword === correctPassword) {\n      setDeveloperMode(true);\n      setShowDeveloperPanel(true);\n      setDeveloperPassword('');\n    } else {\n      alert('كلمة المرور غير صحيحة');\n      setDeveloperPassword('');\n    }\n  };\n\n  const qualityOptions = [\n    { value: 'auto', label: 'تلقائي' },\n    { value: 'hd2160', label: '4K (2160p)' },\n    { value: 'hd1440', label: '1440p' },\n    { value: 'hd1080', label: '1080p' },\n    { value: 'hd720', label: '720p' },\n    { value: 'large', label: '480p' },\n    { value: 'medium', label: '360p' },\n    { value: 'small', label: '240p' }\n  ];\n\n  const adStats = adBlocker.getStats();\n  const monetizationStats = developerMonetization.getAnalytics();\n\n  return (\n    <>\n      <div className=\"settings-overlay\" onClick={onClose}>\n        <div className=\"settings-modal\" onClick={(e) => e.stopPropagation()}>\n          <div className=\"settings-header\">\n            <h2 className=\"settings-title\">إعدادات التطبيق</h2>\n            <button className=\"close-btn\" onClick={onClose}>✕</button>\n          </div>\n\n          <div className=\"settings-section\">\n            <h3 className=\"section-title\">إعدادات الفيديو</h3>\n            \n            <div className=\"setting-item\">\n              <span className=\"setting-label\">جودة الفيديو الافتراضية</span>\n              <div className=\"setting-control\">\n                <select \n                  value={settings.quality} \n                  onChange={handleQualityChange}\n                >\n                  {qualityOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">إخفاء عناصر التحكم</span>\n              <div \n                className={`toggle-switch ${settings.hideControls ? 'active' : ''}`}\n                onClick={() => handleToggle('hideControls')}\n              />\n            </div>\n\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">مستوى الصوت</span>\n              <div className=\"setting-control\">\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max=\"100\"\n                  value={settings.volume || 100}\n                  onChange={(e) => onSettingsChange({ volume: parseInt(e.target.value) })}\n                  className=\"volume-slider\"\n                />\n                <span>{settings.volume || 100}%</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"settings-section\">\n            <h3 className=\"section-title\">إعدادات التطبيق</h3>\n            \n            <div className=\"setting-item\">\n              <span className=\"setting-label\">مانع الإعلانات</span>\n              <div \n                className={`toggle-switch ${settings.adBlock ? 'active' : ''}`}\n                onClick={() => handleToggle('adBlock')}\n              />\n            </div>\n\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">الثيم الداكن</span>\n              <div \n                className={`toggle-switch ${settings.darkTheme ? 'active' : ''}`}\n                onClick={() => handleToggle('darkTheme')}\n              />\n            </div>\n\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">التشغيل التلقائي</span>\n              <div \n                className={`toggle-switch ${settings.autoplay ? 'active' : ''}`}\n                onClick={() => handleToggle('autoplay')}\n              />\n            </div>\n          </div>\n\n          <div className=\"settings-section\">\n            <h3 className=\"section-title\">إحصائيات الاستخدام</h3>\n            \n            <div className=\"stats-grid\">\n              <div className=\"stat-card\">\n                <div className=\"stat-value\">{stats?.videosWatched || 0}</div>\n                <div className=\"stat-label\">فيديوهات مشاهدة</div>\n              </div>\n\n              <div className=\"stat-card\">\n                <div className=\"stat-value\">{adStats.totalBlocked}</div>\n                <div className=\"stat-label\">إعلانات محجوبة</div>\n              </div>\n\n              <div className=\"stat-card\">\n                <div className=\"stat-value\">{stats?.timeSpent || 0}د</div>\n                <div className=\"stat-label\">وقت الاستخدام</div>\n              </div>\n\n              <div className=\"stat-card\">\n                <div className=\"stat-value\">{monetizationStats.impressions}</div>\n                <div className=\"stat-label\">مرات الظهور</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"settings-section\">\n            <h3 className=\"section-title\">معلومات التطبيق</h3>\n            \n            <div className=\"setting-item\">\n              <span className=\"setting-label\">الإصدار</span>\n              <span>1.0.0</span>\n            </div>\n\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">المطور</span>\n              <span>YouTube Player Developer</span>\n            </div>\n\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">آخر تحديث</span>\n              <span>{new Date().toLocaleDateString('ar-SA')}</span>\n            </div>\n          </div>\n\n          {/* قسم المطور المخفي */}\n          <div className=\"settings-section\">\n            <h3 className=\"section-title\">إعدادات متقدمة</h3>\n            \n            {!developerMode ? (\n              <div className=\"developer-access\">\n                <div className=\"setting-item\">\n                  <span className=\"setting-label\">وضع المطور</span>\n                  <div className=\"developer-login\">\n                    <input\n                      type=\"password\"\n                      placeholder=\"كلمة مرور المطور\"\n                      value={developerPassword}\n                      onChange={(e) => setDeveloperPassword(e.target.value)}\n                      onKeyPress={(e) => e.key === 'Enter' && handleDeveloperAccess()}\n                    />\n                    <button onClick={handleDeveloperAccess}>دخول</button>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"developer-controls\">\n                <button \n                  className=\"developer-panel-btn\"\n                  onClick={() => setShowDeveloperPanel(true)}\n                >\n                  💼 فتح لوحة تحكم المطور\n                </button>\n                \n                <div className=\"developer-stats\">\n                  <p>🎯 معدل النقر: {monetizationStats.ctr}</p>\n                  <p>💰 الأرباح: {monetizationStats.revenueFormatted}</p>\n                  <p>📊 التحويلات: {monetizationStats.conversions}</p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div style={{ textAlign: 'center', marginTop: '20px', color: '#b3b3b3', fontSize: '12px' }}>\n            <p>مشغل يوتيوب مخصص مع واجهة عربية</p>\n            <p>جميع الحقوق محفوظة © 2024</p>\n          </div>\n        </div>\n      </div>\n\n      {showDeveloperPanel && (\n        <DeveloperPanel onClose={() => setShowDeveloperPanel(false)} />\n      )}\n    </>\n  );\n};\n\nexport default SettingsEnhanced;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,OAASC,SAAS,KAAQ,oBAAoB,CAC9C,OAASC,qBAAqB,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvE,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAAoD,IAAnD,CAAEC,QAAQ,CAAEC,gBAAgB,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAAJ,IAAA,CACtE,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACkB,aAAa,CAAEC,gBAAgB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAAsB,mBAAmB,CAAIC,CAAC,EAAK,CACjCV,gBAAgB,CAAC,CAAEW,OAAO,CAAED,CAAC,CAACE,MAAM,CAACC,KAAM,CAAC,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAC,YAAY,CAAIC,OAAO,EAAK,CAChCf,gBAAgB,CAAC,CAAE,CAACe,OAAO,EAAG,CAAChB,QAAQ,CAACgB,OAAO,CAAE,CAAC,CAAC,CACrD,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClC;AACA,KAAM,CAAAC,eAAe,CAAG,SAAS,CAAE;AAEnC,GAAIV,iBAAiB,GAAKU,eAAe,CAAE,CACzCX,gBAAgB,CAAC,IAAI,CAAC,CACtBF,qBAAqB,CAAC,IAAI,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,IAAM,CACLU,KAAK,CAAC,uBAAuB,CAAC,CAC9BV,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CACF,CAAC,CAED,KAAM,CAAAW,cAAc,CAAG,CACrB,CAAEN,KAAK,CAAE,MAAM,CAAEO,KAAK,CAAE,QAAS,CAAC,CAClC,CAAEP,KAAK,CAAE,QAAQ,CAAEO,KAAK,CAAE,YAAa,CAAC,CACxC,CAAEP,KAAK,CAAE,QAAQ,CAAEO,KAAK,CAAE,OAAQ,CAAC,CACnC,CAAEP,KAAK,CAAE,QAAQ,CAAEO,KAAK,CAAE,OAAQ,CAAC,CACnC,CAAEP,KAAK,CAAE,OAAO,CAAEO,KAAK,CAAE,MAAO,CAAC,CACjC,CAAEP,KAAK,CAAE,OAAO,CAAEO,KAAK,CAAE,MAAO,CAAC,CACjC,CAAEP,KAAK,CAAE,QAAQ,CAAEO,KAAK,CAAE,MAAO,CAAC,CAClC,CAAEP,KAAK,CAAE,OAAO,CAAEO,KAAK,CAAE,MAAO,CAAC,CAClC,CAED,KAAM,CAAAC,OAAO,CAAGhC,SAAS,CAACiC,QAAQ,CAAC,CAAC,CACpC,KAAM,CAAAC,iBAAiB,CAAGjC,qBAAqB,CAACkC,YAAY,CAAC,CAAC,CAE9D,mBACE9B,KAAA,CAAAE,SAAA,EAAA6B,QAAA,eACEjC,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAACC,OAAO,CAAE1B,OAAQ,CAAAwB,QAAA,cACjD/B,KAAA,QAAKgC,SAAS,CAAC,gBAAgB,CAACC,OAAO,CAAGjB,CAAC,EAAKA,CAAC,CAACkB,eAAe,CAAC,CAAE,CAAAH,QAAA,eAClE/B,KAAA,QAAKgC,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BjC,IAAA,OAAIkC,SAAS,CAAC,gBAAgB,CAAAD,QAAA,CAAC,uFAAe,CAAI,CAAC,cACnDjC,IAAA,WAAQkC,SAAS,CAAC,WAAW,CAACC,OAAO,CAAE1B,OAAQ,CAAAwB,QAAA,CAAC,QAAC,CAAQ,CAAC,EACvD,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BjC,IAAA,OAAIkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uFAAe,CAAI,CAAC,cAElD/B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,kIAAuB,CAAM,CAAC,cAC9DjC,IAAA,QAAKkC,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BjC,IAAA,WACEqB,KAAK,CAAEd,QAAQ,CAACY,OAAQ,CACxBkB,QAAQ,CAAEpB,mBAAoB,CAAAgB,QAAA,CAE7BN,cAAc,CAACW,GAAG,CAACC,MAAM,eACxBvC,IAAA,WAA2BqB,KAAK,CAAEkB,MAAM,CAAClB,KAAM,CAAAY,QAAA,CAC5CM,MAAM,CAACX,KAAK,EADFW,MAAM,CAAClB,KAEZ,CACT,CAAC,CACI,CAAC,CACN,CAAC,EACH,CAAC,cAENnB,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oGAAkB,CAAM,CAAC,cACzDjC,IAAA,QACEkC,SAAS,CAAE,iBAAiB3B,QAAQ,CAACiC,YAAY,CAAG,QAAQ,CAAG,EAAE,EAAG,CACpEL,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,cAAc,CAAE,CAC7C,CAAC,EACC,CAAC,cAENpB,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,+DAAW,CAAM,CAAC,cAClD/B,KAAA,QAAKgC,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BjC,IAAA,UACEyC,IAAI,CAAC,OAAO,CACZC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACTtB,KAAK,CAAEd,QAAQ,CAACqC,MAAM,EAAI,GAAI,CAC9BP,QAAQ,CAAGnB,CAAC,EAAKV,gBAAgB,CAAC,CAAEoC,MAAM,CAAEC,QAAQ,CAAC3B,CAAC,CAACE,MAAM,CAACC,KAAK,CAAE,CAAC,CAAE,CACxEa,SAAS,CAAC,eAAe,CAC1B,CAAC,cACFhC,KAAA,SAAA+B,QAAA,EAAO1B,QAAQ,CAACqC,MAAM,EAAI,GAAG,CAAC,GAAC,EAAM,CAAC,EACnC,CAAC,EACH,CAAC,EACH,CAAC,cAEN1C,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BjC,IAAA,OAAIkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uFAAe,CAAI,CAAC,cAElD/B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iFAAc,CAAM,CAAC,cACrDjC,IAAA,QACEkC,SAAS,CAAE,iBAAiB3B,QAAQ,CAACuC,OAAO,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC/DX,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,SAAS,CAAE,CACxC,CAAC,EACC,CAAC,cAENpB,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qEAAY,CAAM,CAAC,cACnDjC,IAAA,QACEkC,SAAS,CAAE,iBAAiB3B,QAAQ,CAACwC,SAAS,CAAG,QAAQ,CAAG,EAAE,EAAG,CACjEZ,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,WAAW,CAAE,CAC1C,CAAC,EACC,CAAC,cAENpB,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,6FAAgB,CAAM,CAAC,cACvDjC,IAAA,QACEkC,SAAS,CAAE,iBAAiB3B,QAAQ,CAACyC,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAChEb,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,UAAU,CAAE,CACzC,CAAC,EACC,CAAC,EACH,CAAC,cAENpB,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BjC,IAAA,OAAIkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yGAAkB,CAAI,CAAC,cAErD/B,KAAA,QAAKgC,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzB/B,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBjC,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAE,CAAAvB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEuC,aAAa,GAAI,CAAC,CAAM,CAAC,cAC7DjD,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,uFAAe,CAAK,CAAC,EAC9C,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBjC,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEJ,OAAO,CAACqB,YAAY,CAAM,CAAC,cACxDlD,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,iFAAc,CAAK,CAAC,EAC7C,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxB/B,KAAA,QAAKgC,SAAS,CAAC,YAAY,CAAAD,QAAA,EAAE,CAAAvB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyC,SAAS,GAAI,CAAC,CAAC,QAAC,EAAK,CAAC,cAC1DnD,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,2EAAa,CAAK,CAAC,EAC5C,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBjC,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEF,iBAAiB,CAACqB,WAAW,CAAM,CAAC,cACjEpD,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,+DAAW,CAAK,CAAC,EAC1C,CAAC,EACH,CAAC,EACH,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BjC,IAAA,OAAIkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uFAAe,CAAI,CAAC,cAElD/B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC9CjC,IAAA,SAAAiC,QAAA,CAAM,OAAK,CAAM,CAAC,EACf,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC7CjC,IAAA,SAAAiC,QAAA,CAAM,0BAAwB,CAAM,CAAC,EAClC,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,mDAAS,CAAM,CAAC,cAChDjC,IAAA,SAAAiC,QAAA,CAAO,GAAI,CAAAoB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAO,CAAC,EAClD,CAAC,EACH,CAAC,cAGNpD,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BjC,IAAA,OAAIkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iFAAc,CAAI,CAAC,CAEhD,CAACpB,aAAa,cACbb,IAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/B/B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3BjC,IAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yDAAU,CAAM,CAAC,cACjD/B,KAAA,QAAKgC,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BjC,IAAA,UACEyC,IAAI,CAAC,UAAU,CACfc,WAAW,CAAC,wFAAkB,CAC9BlC,KAAK,CAAEN,iBAAkB,CACzBsB,QAAQ,CAAGnB,CAAC,EAAKF,oBAAoB,CAACE,CAAC,CAACE,MAAM,CAACC,KAAK,CAAE,CACtDmC,UAAU,CAAGtC,CAAC,EAAKA,CAAC,CAACuC,GAAG,GAAK,OAAO,EAAIjC,qBAAqB,CAAC,CAAE,CACjE,CAAC,cACFxB,IAAA,WAAQmC,OAAO,CAAEX,qBAAsB,CAAAS,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAClD,CAAC,EACH,CAAC,CACH,CAAC,cAEN/B,KAAA,QAAKgC,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eACjCjC,IAAA,WACEkC,SAAS,CAAC,qBAAqB,CAC/BC,OAAO,CAAEA,CAAA,GAAMvB,qBAAqB,CAAC,IAAI,CAAE,CAAAqB,QAAA,CAC5C,wHAED,CAAQ,CAAC,cAET/B,KAAA,QAAKgC,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/B,KAAA,MAAA+B,QAAA,EAAG,wEAAe,CAACF,iBAAiB,CAAC2B,GAAG,EAAI,CAAC,cAC7CxD,KAAA,MAAA+B,QAAA,EAAG,2DAAY,CAACF,iBAAiB,CAAC4B,gBAAgB,EAAI,CAAC,cACvDzD,KAAA,MAAA+B,QAAA,EAAG,uEAAc,CAACF,iBAAiB,CAAC6B,WAAW,EAAI,CAAC,EACjD,CAAC,EACH,CACN,EACE,CAAC,cAEN1D,KAAA,QAAK2D,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,SAAS,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAS,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAhC,QAAA,eACzFjC,IAAA,MAAAiC,QAAA,CAAG,mKAA+B,CAAG,CAAC,cACtCjC,IAAA,MAAAiC,QAAA,CAAG,8GAAyB,CAAG,CAAC,EAC7B,CAAC,EACH,CAAC,CACH,CAAC,CAELtB,kBAAkB,eACjBX,IAAA,CAACJ,cAAc,EAACa,OAAO,CAAEA,CAAA,GAAMG,qBAAqB,CAAC,KAAK,CAAE,CAAE,CAC/D,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}