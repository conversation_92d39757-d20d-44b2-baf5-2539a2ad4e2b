import React, { useState, useEffect } from 'react';
import { adBlocker, monetization } from '../utils/adBlocker';

const MonetizationPanel = ({ onClose }) => {
  const [adStats, setAdStats] = useState(adBlocker.getStats());
  const [earnings, setEarnings] = useState(monetization.getEarnings());
  const [activeTab, setActiveTab] = useState('adblock');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setAdStats(adBlocker.getStats());
      setEarnings(monetization.getEarnings());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleToggleAdBlock = () => {
    const newState = adBlocker.toggle();
    setAdStats(adBlocker.getStats());
    setMessage(newState ? 'تم تفعيل مانع الإعلانات' : 'تم إلغاء تفعيل مانع الإعلانات');
    setTimeout(() => setMessage(''), 3000);
  };

  const handleResetStats = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإحصائيات؟')) {
      adBlocker.resetStats();
      setAdStats(adBlocker.getStats());
      setMessage('تم إعادة تعيين الإحصائيات');
      setTimeout(() => setMessage(''), 3000);
    }
  };

  const handleWithdraw = () => {
    const amount = parseFloat(withdrawAmount);
    const result = monetization.withdraw(amount);
    
    setMessage(result.message);
    setEarnings(monetization.getEarnings());
    
    if (result.success) {
      setWithdrawAmount('');
    }
    
    setTimeout(() => setMessage(''), 5000);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('ar-SA').format(num);
  };

  return (
    <div className="monetization-overlay" onClick={onClose}>
      <div className="monetization-panel" onClick={(e) => e.stopPropagation()}>
        <div className="panel-header">
          <h2>🛡️ مانع الإعلانات والأرباح</h2>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="panel-tabs">
          <button 
            className={`tab-btn ${activeTab === 'adblock' ? 'active' : ''}`}
            onClick={() => setActiveTab('adblock')}
          >
            🚫 مانع الإعلانات
          </button>
          <button 
            className={`tab-btn ${activeTab === 'earnings' ? 'active' : ''}`}
            onClick={() => setActiveTab('earnings')}
          >
            💰 الأرباح
          </button>
        </div>

        {message && (
          <div className={`message ${message.includes('خطأ') || message.includes('غير') ? 'error' : 'success'}`}>
            {message}
          </div>
        )}

        {activeTab === 'adblock' && (
          <div className="tab-content">
            <div className="stats-overview">
              <div className="stat-card">
                <div className="stat-icon">🛡️</div>
                <div className="stat-info">
                  <div className="stat-value">{formatNumber(adStats.totalBlocked)}</div>
                  <div className="stat-label">إجمالي الإعلانات المحجوبة</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">📅</div>
                <div className="stat-info">
                  <div className="stat-value">{formatNumber(adStats.todayBlocked)}</div>
                  <div className="stat-label">محجوبة اليوم</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">🎯</div>
                <div className="stat-info">
                  <div className="stat-value">{formatNumber(adStats.sessionBlocked)}</div>
                  <div className="stat-label">محجوبة في هذه الجلسة</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">🌐</div>
                <div className="stat-info">
                  <div className="stat-value">{adStats.blockedDomains.length}</div>
                  <div className="stat-label">نطاقات محجوبة</div>
                </div>
              </div>
            </div>

            <div className="control-section">
              <div className="control-item">
                <span>حالة مانع الإعلانات</span>
                <div className="control-actions">
                  <div className={`status-indicator ${adStats.isEnabled ? 'active' : 'inactive'}`}>
                    {adStats.isEnabled ? '🟢 مفعل' : '🔴 معطل'}
                  </div>
                  <button 
                    className="control-btn"
                    onClick={handleToggleAdBlock}
                  >
                    {adStats.isEnabled ? 'إلغاء التفعيل' : 'تفعيل'}
                  </button>
                </div>
              </div>

              <div className="control-item">
                <span>إعادة تعيين الإحصائيات</span>
                <button 
                  className="control-btn danger"
                  onClick={handleResetStats}
                >
                  إعادة تعيين
                </button>
              </div>
            </div>

            {adStats.blockedDomains.length > 0 && (
              <div className="blocked-domains">
                <h4>النطاقات المحجوبة:</h4>
                <div className="domains-list">
                  {adStats.blockedDomains.slice(0, 10).map((domain, index) => (
                    <span key={index} className="domain-tag">{domain}</span>
                  ))}
                  {adStats.blockedDomains.length > 10 && (
                    <span className="domain-tag more">+{adStats.blockedDomains.length - 10} أكثر</span>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'earnings' && (
          <div className="tab-content">
            <div className="earnings-overview">
              <div className="earning-card main">
                <div className="earning-icon">💰</div>
                <div className="earning-info">
                  <div className="earning-value">{earnings.formatted.total}</div>
                  <div className="earning-label">إجمالي الأرباح</div>
                </div>
              </div>

              <div className="earning-card">
                <div className="earning-icon">📅</div>
                <div className="earning-info">
                  <div className="earning-value">{earnings.formatted.today}</div>
                  <div className="earning-label">أرباح اليوم</div>
                </div>
              </div>

              <div className="earning-card">
                <div className="earning-icon">📊</div>
                <div className="earning-info">
                  <div className="earning-value">{earnings.formatted.thisMonth}</div>
                  <div className="earning-label">أرباح هذا الشهر</div>
                </div>
              </div>
            </div>

            <div className="earning-info-section">
              <h4>كيف تعمل الأرباح؟</h4>
              <ul>
                <li>🎥 احصل على مكافآت عند مشاهدة الفيديوهات</li>
                <li>⏰ أرباح تلقائية كل دقيقة من الاستخدام</li>
                <li>🚫 مكافآت إضافية لحجب الإعلانات</li>
                <li>💎 لا تحتاج لأي تدخل منك</li>
              </ul>
            </div>

            <div className="withdraw-section">
              <h4>سحب الأرباح</h4>
              <div className="withdraw-form">
                <input
                  type="number"
                  placeholder="المبلغ (الحد الأدنى $1.00)"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  min="1"
                  step="0.01"
                />
                <button 
                  className="withdraw-btn"
                  onClick={handleWithdraw}
                  disabled={!withdrawAmount || parseFloat(withdrawAmount) < 1}
                >
                  سحب
                </button>
              </div>
              <p className="withdraw-note">
                * الحد الأدنى للسحب هو $1.00
              </p>
            </div>

            <div className="earning-tips">
              <h4>💡 نصائح لزيادة الأرباح:</h4>
              <ul>
                <li>استخدم التطبيق بانتظام</li>
                <li>شاهد الفيديوهات حتى النهاية</li>
                <li>حافظ على تفعيل مانع الإعلانات</li>
                <li>شارك التطبيق مع الأصدقاء</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MonetizationPanel;

