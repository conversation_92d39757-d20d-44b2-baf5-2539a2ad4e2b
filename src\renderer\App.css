/* الخطوط والألوان الأساسية */
:root {
  --primary-bg: #1a1a1a;
  --secondary-bg: #2d2d2d;
  --accent-bg: #3d3d3d;
  --primary-text: #ffffff;
  --secondary-text: #b3b3b3;
  --accent-color: #ff0000;
  --border-color: #404040;
  --hover-bg: #404040;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;
  background-color: var(--primary-bg);
  color: var(--primary-text);
  direction: rtl;
  overflow: hidden;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-bg);
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-bg);
}

/* شريط العنوان */
.header {
  background-color: var(--secondary-bg);
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--accent-color);
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--accent-bg);
  border-radius: 25px;
  padding: 8px 15px;
  min-width: 400px;
}

.search-input {
  background: none;
  border: none;
  color: var(--primary-text);
  font-size: 14px;
  width: 100%;
  outline: none;
  direction: rtl;
}

.search-input::placeholder {
  color: var(--secondary-text);
}

.search-btn {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  padding: 5px;
  margin-left: 10px;
}

.search-btn:hover {
  color: var(--primary-text);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-btn {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  padding: 8px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.header-btn:hover {
  background-color: var(--hover-bg);
  color: var(--primary-text);
}

/* الشريط الجانبي */
.sidebar {
  width: 300px;
  background-color: var(--secondary-bg);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.video-item {
  display: flex;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 10px;
}

.video-item:hover {
  background-color: var(--hover-bg);
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 5px;
  object-fit: cover;
}

.video-info {
  flex: 1;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-channel {
  font-size: 12px;
  color: var(--secondary-text);
}

/* مشغل يوتيوب */
.youtube-player-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  position: relative;
}

.youtube-player {
  width: 100%;
  height: 100%;
}

.player-placeholder {
  text-align: center;
  color: var(--secondary-text);
}

.player-placeholder h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

.player-placeholder p {
  font-size: 16px;
}

/* نافذة الإعدادات */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-modal {
  background-color: var(--secondary-bg);
  border-radius: 10px;
  padding: 30px;
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.settings-title {
  font-size: 20px;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  font-size: 20px;
  padding: 5px;
}

.close-btn:hover {
  color: var(--primary-text);
}

.settings-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--accent-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: var(--accent-bg);
  border-radius: 8px;
}

.setting-label {
  font-size: 14px;
}

.setting-control select {
  background-color: var(--primary-bg);
  color: var(--primary-text);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 14px;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 25px;
  background-color: var(--border-color);
  border-radius: 25px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background-color: var(--accent-color);
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 2px;
  right: 2px;
  width: 21px;
  height: 21px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.toggle-switch.active::after {
  transform: translateX(-25px);
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }
  
  .search-container {
    min-width: 250px;
  }
  
  .settings-modal {
    width: 90vw;
    padding: 20px;
  }
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--hover-bg);
}

