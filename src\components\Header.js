import React, { useState } from 'react';

const Header = ({ onVideoLoad, onSettingsClick }) => {
  const [searchInput, setSearchInput] = useState('');

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchInput.trim()) {
      onVideoLoad(searchInput.trim());
      setSearchInput('');
    }
  };

  const handleMinimize = () => {
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      ipcRenderer.invoke('minimize-window');
    }
  };

  const handleMaximize = () => {
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      ipcRenderer.invoke('maximize-window');
    }
  };

  const handleClose = () => {
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      ipcRenderer.invoke('close-window');
    }
  };

  return (
    <header className="header">
      <div className="header-right">
        <div className="logo">
          <span>📺</span>
          <span>مشغل يوتيوب</span>
        </div>
        
        <form onSubmit={handleSearch} className="search-container">
          <input
            type="text"
            className="search-input"
            placeholder="ابحث في يوتيوب أو الصق رابط الفيديو..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
          <button type="submit" className="search-btn">
            🔍
          </button>
        </form>
      </div>

      <div className="header-left">
        <button 
          className="header-btn"
          onClick={onSettingsClick}
          title="الإعدادات"
        >
          ⚙️
        </button>
        
        <button 
          className="header-btn"
          onClick={handleMinimize}
          title="تصغير"
        >
          ➖
        </button>
        
        <button 
          className="header-btn"
          onClick={handleMaximize}
          title="تكبير/استعادة"
        >
          ⬜
        </button>
        
        <button 
          className="header-btn"
          onClick={handleClose}
          title="إغلاق"
        >
          ❌
        </button>
      </div>
    </header>
  );
};

export default Header;

