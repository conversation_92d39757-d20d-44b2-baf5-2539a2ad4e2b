{"ast": null, "code": "// مساعدات YouTube API\nexport class YouTubeHelper{static extractVideoId(url){const regex=/(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;const match=url.match(regex);return match?match[1]:null;}static generateThumbnailUrl(videoId){let quality=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'mqdefault';return`https://img.youtube.com/vi/${videoId}/${quality}.jpg`;}static formatDuration(seconds){const hours=Math.floor(seconds/3600);const minutes=Math.floor(seconds%3600/60);const secs=seconds%60;if(hours>0){return`${hours}:${minutes.toString().padStart(2,'0')}:${secs.toString().padStart(2,'0')}`;}return`${minutes}:${secs.toString().padStart(2,'0')}`;}static getQualityLabel(quality){const qualityMap={'auto':'تلقائي','hd2160':'4K (2160p)','hd1440':'1440p','hd1080':'1080p','hd720':'720p','large':'480p','medium':'360p','small':'240p'};return qualityMap[quality]||quality;}// محاكاة بحث YouTube (في التطبيق الحقيقي ستحتاج YouTube Data API)\nstatic async searchVideos(query){let maxResults=arguments.length>1&&arguments[1]!==undefined?arguments[1]:10;// بيانات وهمية للعرض التوضيحي\nconst mockVideos=[{id:'dQw4w9WgXcQ',title:`${query} - فيديو تجريبي 1`,channel:'قناة تجريبية',duration:'3:32',views:'1.2M',publishedAt:'2023-01-15'},{id:'kJQP7kiw5Fk',title:`محتوى حول ${query}`,channel:'قناة المحتوى',duration:'4:42',views:'8.5M',publishedAt:'2023-02-20'},{id:'fJ9rUzIMcZQ',title:`شرح ${query} بالتفصيل`,channel:'قناة التعليم',duration:'5:55',views:'3.1M',publishedAt:'2023-03-10'},{id:'JGwWNGJdvx8',title:`أفضل ${query} لهذا العام`,channel:'قناة المراجعات',duration:'2:18',views:'950K',publishedAt:'2023-04-05'},{id:'RgKAFK5djSk',title:`كيفية استخدام ${query}`,channel:'قناة الشروحات',duration:'6:12',views:'2.7M',publishedAt:'2023-05-12'}];// محاكاة تأخير الشبكة\nawait new Promise(resolve=>setTimeout(resolve,800));return mockVideos.slice(0,maxResults).map(video=>({...video,thumbnail:this.generateThumbnailUrl(video.id)}));}// الحصول على معلومات الفيديو\nstatic async getVideoInfo(videoId){// بيانات وهمية للعرض التوضيحي\nconst mockInfo={id:videoId,title:'عنوان الفيديو',channel:'اسم القناة',description:'وصف الفيديو...',duration:'3:45',views:'1.5M',likes:'45K',publishedAt:'2023-06-01',thumbnail:this.generateThumbnailUrl(videoId)};await new Promise(resolve=>setTimeout(resolve,300));return mockInfo;}}// إعدادات مانع الإعلانات\nexport const adBlockFilters=[// إعلانات Google/YouTube\n'*://*.doubleclick.net/*','*://*.googleadservices.com/*','*://*.googlesyndication.com/*','*://googleads.g.doubleclick.net/*',// إعلانات YouTube المحددة\n'*://*.youtube.com/api/stats/ads*','*://*.youtube.com/ptracking*','*://*.youtube.com/pagead/*','*://www.youtube.com/api/stats/ads*','*://www.youtube.com/ptracking*','*://www.youtube.com/pagead/*',// إعلانات أخرى شائعة\n'*://*.adsystem.com/*','*://*.amazon-adsystem.com/*','*://*.facebook.com/tr*','*://*.google-analytics.com/*'];// إعدادات الجودة المتاحة\nexport const qualityOptions=[{value:'auto',label:'تلقائي',description:'أفضل جودة متاحة'},{value:'hd2160',label:'4K (2160p)',description:'جودة فائقة'},{value:'hd1440',label:'1440p',description:'جودة عالية جداً'},{value:'hd1080',label:'1080p',description:'جودة عالية'},{value:'hd720',label:'720p',description:'جودة متوسطة عالية'},{value:'large',label:'480p',description:'جودة متوسطة'},{value:'medium',label:'360p',description:'جودة منخفضة'},{value:'small',label:'240p',description:'جودة منخفضة جداً'}];", "map": {"version": 3, "names": ["YouTubeHelper", "extractVideoId", "url", "regex", "match", "generateThumbnailUrl", "videoId", "quality", "arguments", "length", "undefined", "formatDuration", "seconds", "hours", "Math", "floor", "minutes", "secs", "toString", "padStart", "getQualityLabel", "qualityMap", "searchVideos", "query", "maxResults", "mockVideos", "id", "title", "channel", "duration", "views", "publishedAt", "Promise", "resolve", "setTimeout", "slice", "map", "video", "thumbnail", "getVideoInfo", "mockInfo", "description", "likes", "adBlockFilters", "qualityOptions", "value", "label"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/utils/youtubeApi.js"], "sourcesContent": ["// مساعدات YouTube API\nexport class YouTubeHelper {\n  static extractVideoId(url) {\n    const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n    const match = url.match(regex);\n    return match ? match[1] : null;\n  }\n\n  static generateThumbnailUrl(videoId, quality = 'mqdefault') {\n    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;\n  }\n\n  static formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  static getQualityLabel(quality) {\n    const qualityMap = {\n      'auto': 'تلقائي',\n      'hd2160': '4K (2160p)',\n      'hd1440': '1440p',\n      'hd1080': '1080p',\n      'hd720': '720p',\n      'large': '480p',\n      'medium': '360p',\n      'small': '240p'\n    };\n    return qualityMap[quality] || quality;\n  }\n\n  // محاكاة بحث YouTube (في التطبيق الحقيقي ستحتاج YouTube Data API)\n  static async searchVideos(query, maxResults = 10) {\n    // بيانات وهمية للعرض التوضيحي\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - فيديو تجريبي 1`,\n        channel: 'قناة تجريبية',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `محتوى حول ${query}`,\n        channel: 'قناة المحتوى',\n        duration: '4:42',\n        views: '8.5M',\n        publishedAt: '2023-02-20'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        duration: '5:55',\n        views: '3.1M',\n        publishedAt: '2023-03-10'\n      },\n      {\n        id: 'JGwWNGJdvx8',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        duration: '2:18',\n        views: '950K',\n        publishedAt: '2023-04-05'\n      },\n      {\n        id: 'RgKAFK5djSk',\n        title: `كيفية استخدام ${query}`,\n        channel: 'قناة الشروحات',\n        duration: '6:12',\n        views: '2.7M',\n        publishedAt: '2023-05-12'\n      }\n    ];\n\n    // محاكاة تأخير الشبكة\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    return mockVideos.slice(0, maxResults).map(video => ({\n      ...video,\n      thumbnail: this.generateThumbnailUrl(video.id)\n    }));\n  }\n\n  // الحصول على معلومات الفيديو\n  static async getVideoInfo(videoId) {\n    // بيانات وهمية للعرض التوضيحي\n    const mockInfo = {\n      id: videoId,\n      title: 'عنوان الفيديو',\n      channel: 'اسم القناة',\n      description: 'وصف الفيديو...',\n      duration: '3:45',\n      views: '1.5M',\n      likes: '45K',\n      publishedAt: '2023-06-01',\n      thumbnail: this.generateThumbnailUrl(videoId)\n    };\n\n    await new Promise(resolve => setTimeout(resolve, 300));\n    return mockInfo;\n  }\n}\n\n// إعدادات مانع الإعلانات\nexport const adBlockFilters = [\n  // إعلانات Google/YouTube\n  '*://*.doubleclick.net/*',\n  '*://*.googleadservices.com/*',\n  '*://*.googlesyndication.com/*',\n  '*://googleads.g.doubleclick.net/*',\n  \n  // إعلانات YouTube المحددة\n  '*://*.youtube.com/api/stats/ads*',\n  '*://*.youtube.com/ptracking*',\n  '*://*.youtube.com/pagead/*',\n  '*://www.youtube.com/api/stats/ads*',\n  '*://www.youtube.com/ptracking*',\n  '*://www.youtube.com/pagead/*',\n  \n  // إعلانات أخرى شائعة\n  '*://*.adsystem.com/*',\n  '*://*.amazon-adsystem.com/*',\n  '*://*.facebook.com/tr*',\n  '*://*.google-analytics.com/*'\n];\n\n// إعدادات الجودة المتاحة\nexport const qualityOptions = [\n  { value: 'auto', label: 'تلقائي', description: 'أفضل جودة متاحة' },\n  { value: 'hd2160', label: '4K (2160p)', description: 'جودة فائقة' },\n  { value: 'hd1440', label: '1440p', description: 'جودة عالية جداً' },\n  { value: 'hd1080', label: '1080p', description: 'جودة عالية' },\n  { value: 'hd720', label: '720p', description: 'جودة متوسطة عالية' },\n  { value: 'large', label: '480p', description: 'جودة متوسطة' },\n  { value: 'medium', label: '360p', description: 'جودة منخفضة' },\n  { value: 'small', label: '240p', description: 'جودة منخفضة جداً' }\n];\n\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,aAAc,CACzB,MAAO,CAAAC,cAAcA,CAACC,GAAG,CAAE,CACzB,KAAM,CAAAC,KAAK,CAAG,4FAA4F,CAC1G,KAAM,CAAAC,KAAK,CAAGF,GAAG,CAACE,KAAK,CAACD,KAAK,CAAC,CAC9B,MAAO,CAAAC,KAAK,CAAGA,KAAK,CAAC,CAAC,CAAC,CAAG,IAAI,CAChC,CAEA,MAAO,CAAAC,oBAAoBA,CAACC,OAAO,CAAyB,IAAvB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,WAAW,CACxD,MAAO,8BAA8BF,OAAO,IAAIC,OAAO,MAAM,CAC/D,CAEA,MAAO,CAAAI,cAAcA,CAACC,OAAO,CAAE,CAC7B,KAAM,CAAAC,KAAK,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,IAAI,CAAC,CACxC,KAAM,CAAAI,OAAO,CAAGF,IAAI,CAACC,KAAK,CAAEH,OAAO,CAAG,IAAI,CAAI,EAAE,CAAC,CACjD,KAAM,CAAAK,IAAI,CAAGL,OAAO,CAAG,EAAE,CAEzB,GAAIC,KAAK,CAAG,CAAC,CAAE,CACb,MAAO,GAAGA,KAAK,IAAIG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9F,CACA,MAAO,GAAGH,OAAO,IAAIC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACzD,CAEA,MAAO,CAAAC,eAAeA,CAACb,OAAO,CAAE,CAC9B,KAAM,CAAAc,UAAU,CAAG,CACjB,MAAM,CAAE,QAAQ,CAChB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,OAAO,CACjB,OAAO,CAAE,MAAM,CACf,OAAO,CAAE,MAAM,CACf,QAAQ,CAAE,MAAM,CAChB,OAAO,CAAE,MACX,CAAC,CACD,MAAO,CAAAA,UAAU,CAACd,OAAO,CAAC,EAAIA,OAAO,CACvC,CAEA;AACA,YAAa,CAAAe,YAAYA,CAACC,KAAK,CAAmB,IAAjB,CAAAC,UAAU,CAAAhB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC9C;AACA,KAAM,CAAAiB,UAAU,CAAG,CACjB,CACEC,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,GAAGJ,KAAK,mBAAmB,CAClCK,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YACf,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,aAAaJ,KAAK,EAAE,CAC3BK,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YACf,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,OAAOJ,KAAK,WAAW,CAC9BK,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YACf,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,QAAQJ,KAAK,aAAa,CACjCK,OAAO,CAAE,gBAAgB,CACzBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YACf,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,iBAAiBJ,KAAK,EAAE,CAC/BK,OAAO,CAAE,eAAe,CACxBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,YACf,CAAC,CACF,CAED;AACA,KAAM,IAAI,CAAAC,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD,MAAO,CAAAR,UAAU,CAACU,KAAK,CAAC,CAAC,CAAEX,UAAU,CAAC,CAACY,GAAG,CAACC,KAAK,GAAK,CACnD,GAAGA,KAAK,CACRC,SAAS,CAAE,IAAI,CAACjC,oBAAoB,CAACgC,KAAK,CAACX,EAAE,CAC/C,CAAC,CAAC,CAAC,CACL,CAEA;AACA,YAAa,CAAAa,YAAYA,CAACjC,OAAO,CAAE,CACjC;AACA,KAAM,CAAAkC,QAAQ,CAAG,CACfd,EAAE,CAAEpB,OAAO,CACXqB,KAAK,CAAE,eAAe,CACtBC,OAAO,CAAE,YAAY,CACrBa,WAAW,CAAE,gBAAgB,CAC7BZ,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,MAAM,CACbY,KAAK,CAAE,KAAK,CACZX,WAAW,CAAE,YAAY,CACzBO,SAAS,CAAE,IAAI,CAACjC,oBAAoB,CAACC,OAAO,CAC9C,CAAC,CAED,KAAM,IAAI,CAAA0B,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CACtD,MAAO,CAAAO,QAAQ,CACjB,CACF,CAEA;AACA,MAAO,MAAM,CAAAG,cAAc,CAAG,CAC5B;AACA,yBAAyB,CACzB,8BAA8B,CAC9B,+BAA+B,CAC/B,mCAAmC,CAEnC;AACA,kCAAkC,CAClC,8BAA8B,CAC9B,4BAA4B,CAC5B,oCAAoC,CACpC,gCAAgC,CAChC,8BAA8B,CAE9B;AACA,sBAAsB,CACtB,6BAA6B,CAC7B,wBAAwB,CACxB,8BAA8B,CAC/B,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAG,CAC5B,CAAEC,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,QAAQ,CAAEL,WAAW,CAAE,iBAAkB,CAAC,CAClE,CAAEI,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,YAAY,CAAEL,WAAW,CAAE,YAAa,CAAC,CACnE,CAAEI,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,OAAO,CAAEL,WAAW,CAAE,iBAAkB,CAAC,CACnE,CAAEI,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,OAAO,CAAEL,WAAW,CAAE,YAAa,CAAC,CAC9D,CAAEI,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAM,CAAEL,WAAW,CAAE,mBAAoB,CAAC,CACnE,CAAEI,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAM,CAAEL,WAAW,CAAE,aAAc,CAAC,CAC7D,CAAEI,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,MAAM,CAAEL,WAAW,CAAE,aAAc,CAAC,CAC9D,CAAEI,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAM,CAAEL,WAAW,CAAE,kBAAmB,CAAC,CACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}