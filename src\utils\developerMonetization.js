// نظام الربح للمطور
export class DeveloperMonetization {
  constructor() {
    this.isEnabled = true;
    this.adConfig = {
      bannerAds: true,
      sidebarAds: true,
      videoOverlayAds: false,
      popupAds: false
    };
    
    this.affiliateLinks = {
      enabled: true,
      links: [
        {
          id: 'tech-store',
          name: 'متجر التقنية',
          url: 'https://example.com/tech?ref=youtube-player',
          category: 'تقنية',
          commission: 5
        },
        {
          id: 'software-deals',
          name: 'عروض البرمجيات',
          url: 'https://example.com/software?ref=youtube-player',
          category: 'برمجيات',
          commission: 10
        }
      ]
    };

    this.sponsoredContent = {
      enabled: true,
      content: [
        {
          id: 'sponsor-1',
          title: 'احصل على أفضل VPN',
          description: 'حماية كاملة لتصفحك مع خصم 50%',
          link: 'https://example.com/vpn?ref=youtube-player',
          image: '/assets/sponsor-vpn.jpg',
          type: 'banner'
        },
        {
          id: 'sponsor-2',
          title: 'كورسات البرمجة المجانية',
          description: 'تعلم البرمجة من الصفر حتى الاحتراف',
          link: 'https://example.com/courses?ref=youtube-player',
          image: '/assets/sponsor-courses.jpg',
          type: 'sidebar'
        }
      ]
    };

    this.analytics = {
      impressions: 0,
      clicks: 0,
      conversions: 0,
      revenue: 0
    };

    this.loadAnalytics();
  }

  // تحميل الإحصائيات
  loadAnalytics() {
    const saved = localStorage.getItem('developer-analytics');
    if (saved) {
      try {
        this.analytics = { ...this.analytics, ...JSON.parse(saved) };
      } catch (error) {
        console.error('Error loading analytics:', error);
      }
    }
  }

  // حفظ الإحصائيات
  saveAnalytics() {
    localStorage.setItem('developer-analytics', JSON.stringify(this.analytics));
  }

  // تسجيل عرض إعلان
  recordImpression(adId, adType) {
    this.analytics.impressions++;
    this.saveAnalytics();
    
    // إرسال البيانات لخادم التحليلات (في التطبيق الحقيقي)
    this.sendAnalytics('impression', { adId, adType });
  }

  // تسجيل نقرة على إعلان
  recordClick(adId, adType, revenue = 0) {
    this.analytics.clicks++;
    this.analytics.revenue += revenue;
    this.saveAnalytics();
    
    // إرسال البيانات لخادم التحليلات
    this.sendAnalytics('click', { adId, adType, revenue });
  }

  // تسجيل تحويل (شراء/اشتراك)
  recordConversion(adId, revenue) {
    this.analytics.conversions++;
    this.analytics.revenue += revenue;
    this.saveAnalytics();
    
    this.sendAnalytics('conversion', { adId, revenue });
  }

  // إرسال البيانات للتحليلات (محاكاة)
  sendAnalytics(event, data) {
    // في التطبيق الحقيقي، ستقوم بإرسال البيانات لخادم التحليلات
    console.log(`Analytics Event: ${event}`, data);
    
    // محاكاة إرسال البيانات
    if (navigator.onLine) {
      // fetch('https://your-analytics-server.com/track', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ event, data, timestamp: new Date().toISOString() })
      // });
    }
  }

  // الحصول على إعلان عشوائي للعرض
  getRandomAd(type = 'banner') {
    const ads = this.sponsoredContent.content.filter(ad => ad.type === type);
    if (ads.length === 0) return null;
    
    const randomAd = ads[Math.floor(Math.random() * ads.length)];
    this.recordImpression(randomAd.id, type);
    
    return randomAd;
  }

  // الحصول على رابط تابع عشوائي
  getRandomAffiliateLink(category = null) {
    let links = this.affiliateLinks.links;
    
    if (category) {
      links = links.filter(link => link.category === category);
    }
    
    if (links.length === 0) return null;
    
    return links[Math.floor(Math.random() * links.length)];
  }

  // إضافة محتوى مدعوم جديد
  addSponsoredContent(content) {
    this.sponsoredContent.content.push({
      ...content,
      id: `sponsor-${Date.now()}`
    });
  }

  // إضافة رابط تابع جديد
  addAffiliateLink(link) {
    this.affiliateLinks.links.push({
      ...link,
      id: `affiliate-${Date.now()}`
    });
  }

  // تحديث إعدادات الإعلانات
  updateAdConfig(config) {
    this.adConfig = { ...this.adConfig, ...config };
  }

  // الحصول على الإحصائيات
  getAnalytics() {
    const ctr = this.analytics.impressions > 0 
      ? (this.analytics.clicks / this.analytics.impressions * 100).toFixed(2)
      : 0;
    
    const conversionRate = this.analytics.clicks > 0
      ? (this.analytics.conversions / this.analytics.clicks * 100).toFixed(2)
      : 0;

    return {
      ...this.analytics,
      ctr: `${ctr}%`,
      conversionRate: `${conversionRate}%`,
      revenueFormatted: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'USD'
      }).format(this.analytics.revenue)
    };
  }

  // تفعيل/إلغاء تفعيل نظام الربح
  toggle() {
    this.isEnabled = !this.isEnabled;
    return this.isEnabled;
  }
}

// نظام الإعلانات المدمجة
export class IntegratedAds {
  constructor(monetization) {
    this.monetization = monetization;
    this.adElements = new Map();
    this.rotationInterval = 30000; // تغيير الإعلانات كل 30 ثانية
  }

  // إنشاء إعلان بانر
  createBannerAd(containerId) {
    const ad = this.monetization.getRandomAd('banner');
    if (!ad || !this.monetization.adConfig.bannerAds) return null;

    const adElement = document.createElement('div');
    adElement.className = 'integrated-banner-ad';
    adElement.innerHTML = `
      <div class="ad-content" onclick="window.open('${ad.link}', '_blank')">
        <img src="${ad.image}" alt="${ad.title}" onerror="this.style.display='none'">
        <div class="ad-text">
          <h4>${ad.title}</h4>
          <p>${ad.description}</p>
        </div>
        <div class="ad-label">إعلان</div>
      </div>
    `;

    // تسجيل النقرة
    adElement.addEventListener('click', () => {
      this.monetization.recordClick(ad.id, 'banner', 0.05); // 5 سنت لكل نقرة
    });

    this.adElements.set(containerId, adElement);
    return adElement;
  }

  // إنشاء إعلان جانبي
  createSidebarAd() {
    const ad = this.monetization.getRandomAd('sidebar');
    if (!ad || !this.monetization.adConfig.sidebarAds) return null;

    const adElement = document.createElement('div');
    adElement.className = 'integrated-sidebar-ad';
    adElement.innerHTML = `
      <div class="ad-content" onclick="window.open('${ad.link}', '_blank')">
        <img src="${ad.image}" alt="${ad.title}" onerror="this.style.display='none'">
        <div class="ad-text">
          <h5>${ad.title}</h5>
          <p>${ad.description}</p>
        </div>
        <div class="ad-label">إعلان</div>
      </div>
    `;

    adElement.addEventListener('click', () => {
      this.monetization.recordClick(ad.id, 'sidebar', 0.03);
    });

    return adElement;
  }

  // إنشاء رابط تابع مدمج
  createAffiliateLink(text, category = null) {
    const link = this.monetization.getRandomAffiliateLink(category);
    if (!link || !this.monetization.affiliateLinks.enabled) return text;

    const linkElement = document.createElement('a');
    linkElement.href = link.url;
    linkElement.target = '_blank';
    linkElement.textContent = text;
    linkElement.className = 'affiliate-link';
    linkElement.title = `${link.name} - عمولة ${link.commission}%`;

    linkElement.addEventListener('click', () => {
      this.monetization.recordClick(link.id, 'affiliate', 0.10);
    });

    return linkElement.outerHTML;
  }

  // بدء دوران الإعلانات
  startAdRotation() {
    setInterval(() => {
      this.rotateAds();
    }, this.rotationInterval);
  }

  // تدوير الإعلانات
  rotateAds() {
    this.adElements.forEach((element, containerId) => {
      const container = document.getElementById(containerId);
      if (container && element.parentNode === container) {
        container.removeChild(element);
        const newAd = this.createBannerAd(containerId);
        if (newAd) {
          container.appendChild(newAd);
        }
      }
    });
  }
}

// إنشاء مثيلات عامة
export const developerMonetization = new DeveloperMonetization();
export const integratedAds = new IntegratedAds(developerMonetization);

