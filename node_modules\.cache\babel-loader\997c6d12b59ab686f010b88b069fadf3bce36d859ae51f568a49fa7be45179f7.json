{"ast": null, "code": "// نظام مانع الإعلانات المتقدم\nexport class AdBlocker{constructor(){this.blockedAds=0;this.blockedDomains=new Set();this.isEnabled=true;this.filters=this.loadFilters();this.stats={totalBlocked:0,todayBlocked:0,lastReset:new Date().toDateString()};this.loadStats();}// قوائم فلترة الإعلانات المحدثة\nloadFilters(){return{// إعلانات YouTube\nyoutube:['*://*.doubleclick.net/*','*://*.googleadservices.com/*','*://*.googlesyndication.com/*','*://googleads.g.doubleclick.net/*','*://*.youtube.com/api/stats/ads*','*://*.youtube.com/ptracking*','*://*.youtube.com/pagead/*','*://www.youtube.com/api/stats/ads*','*://www.youtube.com/ptracking*','*://www.youtube.com/pagead/*','*://*.youtube.com/get_video_info*','*://*.youtube.com/youtubei/v1/player/ad_break*'],// إعلانات عامة\ngeneral:['*://*.adsystem.com/*','*://*.amazon-adsystem.com/*','*://*.facebook.com/tr*','*://*.google-analytics.com/*','*://*.googletagmanager.com/*','*://*.scorecardresearch.com/*','*://*.outbrain.com/*','*://*.taboola.com/*','*://*.adsafeprotected.com/*','*://*.moatads.com/*'],// متتبعات\ntrackers:['*://*.hotjar.com/*','*://*.fullstory.com/*','*://*.mixpanel.com/*','*://*.segment.com/*','*://*.amplitude.com/*']};}// تحميل الإحصائيات\nloadStats(){const saved=localStorage.getItem('adblock-stats');if(saved){try{const stats=JSON.parse(saved);// إعادة تعيين الإحصائيات اليومية إذا تغير اليوم\nif(stats.lastReset!==new Date().toDateString()){stats.todayBlocked=0;stats.lastReset=new Date().toDateString();}this.stats=stats;}catch(error){console.error('Error loading ad block stats:',error);}}}// حفظ الإحصائيات\nsaveStats(){localStorage.setItem('adblock-stats',JSON.stringify(this.stats));}// فحص ما إذا كان الرابط يجب حجبه\nshouldBlock(url){if(!this.isEnabled)return false;const allFilters=[...this.filters.youtube,...this.filters.general,...this.filters.trackers];return allFilters.some(filter=>{const regex=new RegExp(filter.replace(/\\*/g,'.*'));return regex.test(url);});}// حجب الإعلان وتحديث الإحصائيات\nblockAd(url){if(this.shouldBlock(url)){this.blockedAds++;this.stats.totalBlocked++;this.stats.todayBlocked++;// استخراج النطاق\ntry{const domain=new URL(url).hostname;this.blockedDomains.add(domain);}catch(error){console.error('Error parsing URL:',error);}this.saveStats();return true;}return false;}// الحصول على الإحصائيات\ngetStats(){return{...this.stats,sessionBlocked:this.blockedAds,blockedDomains:Array.from(this.blockedDomains),isEnabled:this.isEnabled};}// تفعيل/إلغاء تفعيل مانع الإعلانات\ntoggle(){this.isEnabled=!this.isEnabled;return this.isEnabled;}// إعادة تعيين الإحصائيات\nresetStats(){this.stats={totalBlocked:0,todayBlocked:0,lastReset:new Date().toDateString()};this.blockedAds=0;this.blockedDomains.clear();this.saveStats();}// إضافة فلتر مخصص\naddCustomFilter(filter){if(!this.filters.custom){this.filters.custom=[];}this.filters.custom.push(filter);localStorage.setItem('custom-filters',JSON.stringify(this.filters.custom));}// تحميل الفلاتر المخصصة\nloadCustomFilters(){const saved=localStorage.getItem('custom-filters');if(saved){try{this.filters.custom=JSON.parse(saved);}catch(error){console.error('Error loading custom filters:',error);}}}}// نظام الربح المدمج\nexport class MonetizationSystem{constructor(){this.isEnabled=true;this.earnings={total:0,today:0,thisMonth:0,lastUpdate:new Date().toDateString()};this.loadEarnings();this.startEarningSimulation();}// تحميل الأرباح\nloadEarnings(){const saved=localStorage.getItem('monetization-earnings');if(saved){try{const earnings=JSON.parse(saved);// إعادة تعيين الأرباح اليومية إذا تغير اليوم\nif(earnings.lastUpdate!==new Date().toDateString()){earnings.today=0;earnings.lastUpdate=new Date().toDateString();}this.earnings=earnings;}catch(error){console.error('Error loading earnings:',error);}}}// حفظ الأرباح\nsaveEarnings(){localStorage.setItem('monetization-earnings',JSON.stringify(this.earnings));}// محاكاة الأرباح (في التطبيق الحقيقي ستكون مرتبطة بخدمة حقيقية)\nstartEarningSimulation(){if(!this.isEnabled)return;// ربح صغير كل دقيقة (محاكاة)\nsetInterval(()=>{const earning=Math.random()*0.001;// ربح عشوائي صغير\nthis.addEarning(earning);},60000);// كل دقيقة\n// ربح إضافي عند مشاهدة الفيديوهات\nthis.setupVideoWatchingRewards();}// إضافة ربح\naddEarning(amount){this.earnings.total+=amount;this.earnings.today+=amount;this.earnings.thisMonth+=amount;this.saveEarnings();}// إعداد مكافآت مشاهدة الفيديوهات\nsetupVideoWatchingRewards(){// مكافأة عند تشغيل فيديو جديد\nwindow.addEventListener('video-started',()=>{const reward=Math.random()*0.005;this.addEarning(reward);});// مكافأة عند إكمال مشاهدة فيديو\nwindow.addEventListener('video-completed',()=>{const reward=Math.random()*0.01;this.addEarning(reward);});}// الحصول على الأرباح\ngetEarnings(){return{...this.earnings,formatted:{total:this.formatCurrency(this.earnings.total),today:this.formatCurrency(this.earnings.today),thisMonth:this.formatCurrency(this.earnings.thisMonth)}};}// تنسيق العملة\nformatCurrency(amount){return new Intl.NumberFormat('ar-SA',{style:'currency',currency:'USD',minimumFractionDigits:4}).format(amount);}// تفعيل/إلغاء تفعيل نظام الربح\ntoggle(){this.isEnabled=!this.isEnabled;if(this.isEnabled){this.startEarningSimulation();}return this.isEnabled;}// سحب الأرباح (محاكاة)\nwithdraw(amount){if(amount<=this.earnings.total&&amount>=1.0){this.earnings.total-=amount;this.saveEarnings();return{success:true,message:`تم سحب ${this.formatCurrency(amount)} بنجاح`,newBalance:this.formatCurrency(this.earnings.total)};}else{return{success:false,message:'المبلغ غير صالح أو الرصيد غير كافي (الحد الأدنى للسحب $1.00)',newBalance:this.formatCurrency(this.earnings.total)};}}}// إنشاء مثيلات عامة\nexport const adBlocker=new AdBlocker();export const monetization=new MonetizationSystem();", "map": {"version": 3, "names": ["Ad<PERSON><PERSON><PERSON>", "constructor", "blockedAds", "blockedDomains", "Set", "isEnabled", "filters", "loadFilters", "stats", "totalBlocked", "todayBlocked", "last<PERSON><PERSON>t", "Date", "toDateString", "loadStats", "youtube", "general", "trackers", "saved", "localStorage", "getItem", "JSON", "parse", "error", "console", "saveStats", "setItem", "stringify", "shouldBlock", "url", "allFilters", "some", "filter", "regex", "RegExp", "replace", "test", "blockAd", "domain", "URL", "hostname", "add", "getStats", "sessionBlocked", "Array", "from", "toggle", "resetStats", "clear", "addCustomFilter", "custom", "push", "loadCustomFilters", "MonetizationSystem", "earnings", "total", "today", "thisMonth", "lastUpdate", "loadEarnings", "startEarningSimulation", "saveEarnings", "setInterval", "earning", "Math", "random", "addEarning", "setupVideoWatchingRewards", "amount", "window", "addEventListener", "reward", "getEarnings", "formatted", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "withdraw", "success", "message", "newBalance", "ad<PERSON><PERSON><PERSON>", "monetization"], "sources": ["C:/Users/<USER>/Downloads/Programs/youtube-player-app-source/youtube-player-app/src/utils/adBlocker.js"], "sourcesContent": ["// نظام مانع الإعلانات المتقدم\nexport class AdBlocker {\n  constructor() {\n    this.blockedAds = 0;\n    this.blockedDomains = new Set();\n    this.isEnabled = true;\n    this.filters = this.loadFilters();\n    this.stats = {\n      totalBlocked: 0,\n      todayBlocked: 0,\n      lastReset: new Date().toDateString()\n    };\n    \n    this.loadStats();\n  }\n\n  // قوائم فلترة الإعلانات المحدثة\n  loadFilters() {\n    return {\n      // إعلانات YouTube\n      youtube: [\n        '*://*.doubleclick.net/*',\n        '*://*.googleadservices.com/*',\n        '*://*.googlesyndication.com/*',\n        '*://googleads.g.doubleclick.net/*',\n        '*://*.youtube.com/api/stats/ads*',\n        '*://*.youtube.com/ptracking*',\n        '*://*.youtube.com/pagead/*',\n        '*://www.youtube.com/api/stats/ads*',\n        '*://www.youtube.com/ptracking*',\n        '*://www.youtube.com/pagead/*',\n        '*://*.youtube.com/get_video_info*',\n        '*://*.youtube.com/youtubei/v1/player/ad_break*'\n      ],\n      \n      // إعلانات عامة\n      general: [\n        '*://*.adsystem.com/*',\n        '*://*.amazon-adsystem.com/*',\n        '*://*.facebook.com/tr*',\n        '*://*.google-analytics.com/*',\n        '*://*.googletagmanager.com/*',\n        '*://*.scorecardresearch.com/*',\n        '*://*.outbrain.com/*',\n        '*://*.taboola.com/*',\n        '*://*.adsafeprotected.com/*',\n        '*://*.moatads.com/*'\n      ],\n      \n      // متتبعات\n      trackers: [\n        '*://*.hotjar.com/*',\n        '*://*.fullstory.com/*',\n        '*://*.mixpanel.com/*',\n        '*://*.segment.com/*',\n        '*://*.amplitude.com/*'\n      ]\n    };\n  }\n\n  // تحميل الإحصائيات\n  loadStats() {\n    const saved = localStorage.getItem('adblock-stats');\n    if (saved) {\n      try {\n        const stats = JSON.parse(saved);\n        \n        // إعادة تعيين الإحصائيات اليومية إذا تغير اليوم\n        if (stats.lastReset !== new Date().toDateString()) {\n          stats.todayBlocked = 0;\n          stats.lastReset = new Date().toDateString();\n        }\n        \n        this.stats = stats;\n      } catch (error) {\n        console.error('Error loading ad block stats:', error);\n      }\n    }\n  }\n\n  // حفظ الإحصائيات\n  saveStats() {\n    localStorage.setItem('adblock-stats', JSON.stringify(this.stats));\n  }\n\n  // فحص ما إذا كان الرابط يجب حجبه\n  shouldBlock(url) {\n    if (!this.isEnabled) return false;\n\n    const allFilters = [\n      ...this.filters.youtube,\n      ...this.filters.general,\n      ...this.filters.trackers\n    ];\n\n    return allFilters.some(filter => {\n      const regex = new RegExp(filter.replace(/\\*/g, '.*'));\n      return regex.test(url);\n    });\n  }\n\n  // حجب الإعلان وتحديث الإحصائيات\n  blockAd(url) {\n    if (this.shouldBlock(url)) {\n      this.blockedAds++;\n      this.stats.totalBlocked++;\n      this.stats.todayBlocked++;\n      \n      // استخراج النطاق\n      try {\n        const domain = new URL(url).hostname;\n        this.blockedDomains.add(domain);\n      } catch (error) {\n        console.error('Error parsing URL:', error);\n      }\n      \n      this.saveStats();\n      return true;\n    }\n    return false;\n  }\n\n  // الحصول على الإحصائيات\n  getStats() {\n    return {\n      ...this.stats,\n      sessionBlocked: this.blockedAds,\n      blockedDomains: Array.from(this.blockedDomains),\n      isEnabled: this.isEnabled\n    };\n  }\n\n  // تفعيل/إلغاء تفعيل مانع الإعلانات\n  toggle() {\n    this.isEnabled = !this.isEnabled;\n    return this.isEnabled;\n  }\n\n  // إعادة تعيين الإحصائيات\n  resetStats() {\n    this.stats = {\n      totalBlocked: 0,\n      todayBlocked: 0,\n      lastReset: new Date().toDateString()\n    };\n    this.blockedAds = 0;\n    this.blockedDomains.clear();\n    this.saveStats();\n  }\n\n  // إضافة فلتر مخصص\n  addCustomFilter(filter) {\n    if (!this.filters.custom) {\n      this.filters.custom = [];\n    }\n    this.filters.custom.push(filter);\n    localStorage.setItem('custom-filters', JSON.stringify(this.filters.custom));\n  }\n\n  // تحميل الفلاتر المخصصة\n  loadCustomFilters() {\n    const saved = localStorage.getItem('custom-filters');\n    if (saved) {\n      try {\n        this.filters.custom = JSON.parse(saved);\n      } catch (error) {\n        console.error('Error loading custom filters:', error);\n      }\n    }\n  }\n}\n\n// نظام الربح المدمج\nexport class MonetizationSystem {\n  constructor() {\n    this.isEnabled = true;\n    this.earnings = {\n      total: 0,\n      today: 0,\n      thisMonth: 0,\n      lastUpdate: new Date().toDateString()\n    };\n    \n    this.loadEarnings();\n    this.startEarningSimulation();\n  }\n\n  // تحميل الأرباح\n  loadEarnings() {\n    const saved = localStorage.getItem('monetization-earnings');\n    if (saved) {\n      try {\n        const earnings = JSON.parse(saved);\n        \n        // إعادة تعيين الأرباح اليومية إذا تغير اليوم\n        if (earnings.lastUpdate !== new Date().toDateString()) {\n          earnings.today = 0;\n          earnings.lastUpdate = new Date().toDateString();\n        }\n        \n        this.earnings = earnings;\n      } catch (error) {\n        console.error('Error loading earnings:', error);\n      }\n    }\n  }\n\n  // حفظ الأرباح\n  saveEarnings() {\n    localStorage.setItem('monetization-earnings', JSON.stringify(this.earnings));\n  }\n\n  // محاكاة الأرباح (في التطبيق الحقيقي ستكون مرتبطة بخدمة حقيقية)\n  startEarningSimulation() {\n    if (!this.isEnabled) return;\n\n    // ربح صغير كل دقيقة (محاكاة)\n    setInterval(() => {\n      const earning = Math.random() * 0.001; // ربح عشوائي صغير\n      this.addEarning(earning);\n    }, 60000); // كل دقيقة\n\n    // ربح إضافي عند مشاهدة الفيديوهات\n    this.setupVideoWatchingRewards();\n  }\n\n  // إضافة ربح\n  addEarning(amount) {\n    this.earnings.total += amount;\n    this.earnings.today += amount;\n    this.earnings.thisMonth += amount;\n    this.saveEarnings();\n  }\n\n  // إعداد مكافآت مشاهدة الفيديوهات\n  setupVideoWatchingRewards() {\n    // مكافأة عند تشغيل فيديو جديد\n    window.addEventListener('video-started', () => {\n      const reward = Math.random() * 0.005;\n      this.addEarning(reward);\n    });\n\n    // مكافأة عند إكمال مشاهدة فيديو\n    window.addEventListener('video-completed', () => {\n      const reward = Math.random() * 0.01;\n      this.addEarning(reward);\n    });\n  }\n\n  // الحصول على الأرباح\n  getEarnings() {\n    return {\n      ...this.earnings,\n      formatted: {\n        total: this.formatCurrency(this.earnings.total),\n        today: this.formatCurrency(this.earnings.today),\n        thisMonth: this.formatCurrency(this.earnings.thisMonth)\n      }\n    };\n  }\n\n  // تنسيق العملة\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 4\n    }).format(amount);\n  }\n\n  // تفعيل/إلغاء تفعيل نظام الربح\n  toggle() {\n    this.isEnabled = !this.isEnabled;\n    if (this.isEnabled) {\n      this.startEarningSimulation();\n    }\n    return this.isEnabled;\n  }\n\n  // سحب الأرباح (محاكاة)\n  withdraw(amount) {\n    if (amount <= this.earnings.total && amount >= 1.0) {\n      this.earnings.total -= amount;\n      this.saveEarnings();\n      return {\n        success: true,\n        message: `تم سحب ${this.formatCurrency(amount)} بنجاح`,\n        newBalance: this.formatCurrency(this.earnings.total)\n      };\n    } else {\n      return {\n        success: false,\n        message: 'المبلغ غير صالح أو الرصيد غير كافي (الحد الأدنى للسحب $1.00)',\n        newBalance: this.formatCurrency(this.earnings.total)\n      };\n    }\n  }\n}\n\n// إنشاء مثيلات عامة\nexport const adBlocker = new AdBlocker();\nexport const monetization = new MonetizationSystem();\n\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,SAAU,CACrBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,UAAU,CAAG,CAAC,CACnB,IAAI,CAACC,cAAc,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC/B,IAAI,CAACC,SAAS,CAAG,IAAI,CACrB,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CACjC,IAAI,CAACC,KAAK,CAAG,CACXC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CACrC,CAAC,CAED,IAAI,CAACC,SAAS,CAAC,CAAC,CAClB,CAEA;AACAP,WAAWA,CAAA,CAAG,CACZ,MAAO,CACL;AACAQ,OAAO,CAAE,CACP,yBAAyB,CACzB,8BAA8B,CAC9B,+BAA+B,CAC/B,mCAAmC,CACnC,kCAAkC,CAClC,8BAA8B,CAC9B,4BAA4B,CAC5B,oCAAoC,CACpC,gCAAgC,CAChC,8BAA8B,CAC9B,mCAAmC,CACnC,gDAAgD,CACjD,CAED;AACAC,OAAO,CAAE,CACP,sBAAsB,CACtB,6BAA6B,CAC7B,wBAAwB,CACxB,8BAA8B,CAC9B,8BAA8B,CAC9B,+BAA+B,CAC/B,sBAAsB,CACtB,qBAAqB,CACrB,6BAA6B,CAC7B,qBAAqB,CACtB,CAED;AACAC,QAAQ,CAAE,CACR,oBAAoB,CACpB,uBAAuB,CACvB,sBAAsB,CACtB,qBAAqB,CACrB,uBAAuB,CAE3B,CAAC,CACH,CAEA;AACAH,SAASA,CAAA,CAAG,CACV,KAAM,CAAAI,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,CACnD,GAAIF,KAAK,CAAE,CACT,GAAI,CACF,KAAM,CAAAV,KAAK,CAAGa,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,CAE/B;AACA,GAAIV,KAAK,CAACG,SAAS,GAAK,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAE,CACjDL,KAAK,CAACE,YAAY,CAAG,CAAC,CACtBF,KAAK,CAACG,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAC7C,CAEA,IAAI,CAACL,KAAK,CAAGA,KAAK,CACpB,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CACF,CAEA;AACAE,SAASA,CAAA,CAAG,CACVN,YAAY,CAACO,OAAO,CAAC,eAAe,CAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACnB,KAAK,CAAC,CAAC,CACnE,CAEA;AACAoB,WAAWA,CAACC,GAAG,CAAE,CACf,GAAI,CAAC,IAAI,CAACxB,SAAS,CAAE,MAAO,MAAK,CAEjC,KAAM,CAAAyB,UAAU,CAAG,CACjB,GAAG,IAAI,CAACxB,OAAO,CAACS,OAAO,CACvB,GAAG,IAAI,CAACT,OAAO,CAACU,OAAO,CACvB,GAAG,IAAI,CAACV,OAAO,CAACW,QAAQ,CACzB,CAED,MAAO,CAAAa,UAAU,CAACC,IAAI,CAACC,MAAM,EAAI,CAC/B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC,KAAK,CAAE,IAAI,CAAC,CAAC,CACrD,MAAO,CAAAF,KAAK,CAACG,IAAI,CAACP,GAAG,CAAC,CACxB,CAAC,CAAC,CACJ,CAEA;AACAQ,OAAOA,CAACR,GAAG,CAAE,CACX,GAAI,IAAI,CAACD,WAAW,CAACC,GAAG,CAAC,CAAE,CACzB,IAAI,CAAC3B,UAAU,EAAE,CACjB,IAAI,CAACM,KAAK,CAACC,YAAY,EAAE,CACzB,IAAI,CAACD,KAAK,CAACE,YAAY,EAAE,CAEzB;AACA,GAAI,CACF,KAAM,CAAA4B,MAAM,CAAG,GAAI,CAAAC,GAAG,CAACV,GAAG,CAAC,CAACW,QAAQ,CACpC,IAAI,CAACrC,cAAc,CAACsC,GAAG,CAACH,MAAM,CAAC,CACjC,CAAE,MAAOf,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC5C,CAEA,IAAI,CAACE,SAAS,CAAC,CAAC,CAChB,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAEA;AACAiB,QAAQA,CAAA,CAAG,CACT,MAAO,CACL,GAAG,IAAI,CAAClC,KAAK,CACbmC,cAAc,CAAE,IAAI,CAACzC,UAAU,CAC/BC,cAAc,CAAEyC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1C,cAAc,CAAC,CAC/CE,SAAS,CAAE,IAAI,CAACA,SAClB,CAAC,CACH,CAEA;AACAyC,MAAMA,CAAA,CAAG,CACP,IAAI,CAACzC,SAAS,CAAG,CAAC,IAAI,CAACA,SAAS,CAChC,MAAO,KAAI,CAACA,SAAS,CACvB,CAEA;AACA0C,UAAUA,CAAA,CAAG,CACX,IAAI,CAACvC,KAAK,CAAG,CACXC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CACrC,CAAC,CACD,IAAI,CAACX,UAAU,CAAG,CAAC,CACnB,IAAI,CAACC,cAAc,CAAC6C,KAAK,CAAC,CAAC,CAC3B,IAAI,CAACvB,SAAS,CAAC,CAAC,CAClB,CAEA;AACAwB,eAAeA,CAACjB,MAAM,CAAE,CACtB,GAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC4C,MAAM,CAAE,CACxB,IAAI,CAAC5C,OAAO,CAAC4C,MAAM,CAAG,EAAE,CAC1B,CACA,IAAI,CAAC5C,OAAO,CAAC4C,MAAM,CAACC,IAAI,CAACnB,MAAM,CAAC,CAChCb,YAAY,CAACO,OAAO,CAAC,gBAAgB,CAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACrB,OAAO,CAAC4C,MAAM,CAAC,CAAC,CAC7E,CAEA;AACAE,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAlC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CACpD,GAAIF,KAAK,CAAE,CACT,GAAI,CACF,IAAI,CAACZ,OAAO,CAAC4C,MAAM,CAAG7B,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,CACzC,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAA8B,kBAAmB,CAC9BpD,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACI,SAAS,CAAG,IAAI,CACrB,IAAI,CAACiD,QAAQ,CAAG,CACdC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,GAAI,CAAA9C,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CACtC,CAAC,CAED,IAAI,CAAC8C,YAAY,CAAC,CAAC,CACnB,IAAI,CAACC,sBAAsB,CAAC,CAAC,CAC/B,CAEA;AACAD,YAAYA,CAAA,CAAG,CACb,KAAM,CAAAzC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAC3D,GAAIF,KAAK,CAAE,CACT,GAAI,CACF,KAAM,CAAAoC,QAAQ,CAAGjC,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,CAElC;AACA,GAAIoC,QAAQ,CAACI,UAAU,GAAK,GAAI,CAAA9C,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAE,CACrDyC,QAAQ,CAACE,KAAK,CAAG,CAAC,CAClBF,QAAQ,CAACI,UAAU,CAAG,GAAI,CAAA9C,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CACjD,CAEA,IAAI,CAACyC,QAAQ,CAAGA,QAAQ,CAC1B,CAAE,MAAO/B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CACF,CAEA;AACAsC,YAAYA,CAAA,CAAG,CACb1C,YAAY,CAACO,OAAO,CAAC,uBAAuB,CAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAAC2B,QAAQ,CAAC,CAAC,CAC9E,CAEA;AACAM,sBAAsBA,CAAA,CAAG,CACvB,GAAI,CAAC,IAAI,CAACvD,SAAS,CAAE,OAErB;AACAyD,WAAW,CAAC,IAAM,CAChB,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,KAAK,CAAE;AACvC,IAAI,CAACC,UAAU,CAACH,OAAO,CAAC,CAC1B,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACA,IAAI,CAACI,yBAAyB,CAAC,CAAC,CAClC,CAEA;AACAD,UAAUA,CAACE,MAAM,CAAE,CACjB,IAAI,CAACd,QAAQ,CAACC,KAAK,EAAIa,MAAM,CAC7B,IAAI,CAACd,QAAQ,CAACE,KAAK,EAAIY,MAAM,CAC7B,IAAI,CAACd,QAAQ,CAACG,SAAS,EAAIW,MAAM,CACjC,IAAI,CAACP,YAAY,CAAC,CAAC,CACrB,CAEA;AACAM,yBAAyBA,CAAA,CAAG,CAC1B;AACAE,MAAM,CAACC,gBAAgB,CAAC,eAAe,CAAE,IAAM,CAC7C,KAAM,CAAAC,MAAM,CAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,KAAK,CACpC,IAAI,CAACC,UAAU,CAACK,MAAM,CAAC,CACzB,CAAC,CAAC,CAEF;AACAF,MAAM,CAACC,gBAAgB,CAAC,iBAAiB,CAAE,IAAM,CAC/C,KAAM,CAAAC,MAAM,CAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,IAAI,CACnC,IAAI,CAACC,UAAU,CAACK,MAAM,CAAC,CACzB,CAAC,CAAC,CACJ,CAEA;AACAC,WAAWA,CAAA,CAAG,CACZ,MAAO,CACL,GAAG,IAAI,CAAClB,QAAQ,CAChBmB,SAAS,CAAE,CACTlB,KAAK,CAAE,IAAI,CAACmB,cAAc,CAAC,IAAI,CAACpB,QAAQ,CAACC,KAAK,CAAC,CAC/CC,KAAK,CAAE,IAAI,CAACkB,cAAc,CAAC,IAAI,CAACpB,QAAQ,CAACE,KAAK,CAAC,CAC/CC,SAAS,CAAE,IAAI,CAACiB,cAAc,CAAC,IAAI,CAACpB,QAAQ,CAACG,SAAS,CACxD,CACF,CAAC,CACH,CAEA;AACAiB,cAAcA,CAACN,MAAM,CAAE,CACrB,MAAO,IAAI,CAAAO,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACZ,MAAM,CAAC,CACnB,CAEA;AACAtB,MAAMA,CAAA,CAAG,CACP,IAAI,CAACzC,SAAS,CAAG,CAAC,IAAI,CAACA,SAAS,CAChC,GAAI,IAAI,CAACA,SAAS,CAAE,CAClB,IAAI,CAACuD,sBAAsB,CAAC,CAAC,CAC/B,CACA,MAAO,KAAI,CAACvD,SAAS,CACvB,CAEA;AACA4E,QAAQA,CAACb,MAAM,CAAE,CACf,GAAIA,MAAM,EAAI,IAAI,CAACd,QAAQ,CAACC,KAAK,EAAIa,MAAM,EAAI,GAAG,CAAE,CAClD,IAAI,CAACd,QAAQ,CAACC,KAAK,EAAIa,MAAM,CAC7B,IAAI,CAACP,YAAY,CAAC,CAAC,CACnB,MAAO,CACLqB,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,UAAU,IAAI,CAACT,cAAc,CAACN,MAAM,CAAC,QAAQ,CACtDgB,UAAU,CAAE,IAAI,CAACV,cAAc,CAAC,IAAI,CAACpB,QAAQ,CAACC,KAAK,CACrD,CAAC,CACH,CAAC,IAAM,CACL,MAAO,CACL2B,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,8DAA8D,CACvEC,UAAU,CAAE,IAAI,CAACV,cAAc,CAAC,IAAI,CAACpB,QAAQ,CAACC,KAAK,CACrD,CAAC,CACH,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAA8B,SAAS,CAAG,GAAI,CAAArF,SAAS,CAAC,CAAC,CACxC,MAAO,MAAM,CAAAsF,YAAY,CAAG,GAAI,CAAAjC,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}