import React, { useState, useEffect } from 'react';
import { adBlocker } from '../utils/adBlocker';
import { developerMonetization, integratedAds } from '../utils/developerMonetization';

const DeveloperPanel = ({ onClose }) => {
  const [adStats, setAdStats] = useState(adBlocker.getStats());
  const [analytics, setAnalytics] = useState(developerMonetization.getAnalytics());
  const [activeTab, setActiveTab] = useState('overview');
  const [newAd, setNewAd] = useState({
    title: '',
    description: '',
    link: '',
    type: 'banner'
  });
  const [newAffiliate, setNewAffiliate] = useState({
    name: '',
    url: '',
    category: '',
    commission: 5
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setAdStats(adBlocker.getStats());
      setAnalytics(developerMonetization.getAnalytics());
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const handleAddSponsoredContent = () => {
    if (newAd.title && newAd.description && newAd.link) {
      developerMonetization.addSponsoredContent({
        ...newAd,
        image: '/assets/default-ad.jpg' // صورة افتراضية
      });
      
      setNewAd({ title: '', description: '', link: '', type: 'banner' });
      alert('تم إضافة المحتوى المدعوم بنجاح!');
    }
  };

  const handleAddAffiliateLink = () => {
    if (newAffiliate.name && newAffiliate.url) {
      developerMonetization.addAffiliateLink(newAffiliate);
      setNewAffiliate({ name: '', url: '', category: '', commission: 5 });
      alert('تم إضافة الرابط التابع بنجاح!');
    }
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('ar-SA').format(num);
  };

  return (
    <div className="developer-overlay" onClick={onClose}>
      <div className="developer-panel" onClick={(e) => e.stopPropagation()}>
        <div className="panel-header">
          <h2>💼 لوحة تحكم المطور</h2>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="panel-tabs">
          <button 
            className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            📊 نظرة عامة
          </button>
          <button 
            className={`tab-btn ${activeTab === 'ads' ? 'active' : ''}`}
            onClick={() => setActiveTab('ads')}
          >
            📢 إدارة الإعلانات
          </button>
          <button 
            className={`tab-btn ${activeTab === 'affiliates' ? 'active' : ''}`}
            onClick={() => setActiveTab('affiliates')}
          >
            🔗 الروابط التابعة
          </button>
          <button 
            className={`tab-btn ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            ⚙️ الإعدادات
          </button>
        </div>

        {activeTab === 'overview' && (
          <div className="tab-content">
            <div className="revenue-overview">
              <div className="revenue-card main">
                <div className="revenue-icon">💰</div>
                <div className="revenue-info">
                  <div className="revenue-value">{analytics.revenueFormatted}</div>
                  <div className="revenue-label">إجمالي الأرباح</div>
                </div>
              </div>

              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-icon">👁️</div>
                  <div className="stat-info">
                    <div className="stat-value">{formatNumber(analytics.impressions)}</div>
                    <div className="stat-label">مرات الظهور</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">👆</div>
                  <div className="stat-info">
                    <div className="stat-value">{formatNumber(analytics.clicks)}</div>
                    <div className="stat-label">النقرات</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">🎯</div>
                  <div className="stat-info">
                    <div className="stat-value">{analytics.ctr}</div>
                    <div className="stat-label">معدل النقر</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">💎</div>
                  <div className="stat-info">
                    <div className="stat-value">{formatNumber(analytics.conversions)}</div>
                    <div className="stat-label">التحويلات</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">📈</div>
                  <div className="stat-info">
                    <div className="stat-value">{analytics.conversionRate}</div>
                    <div className="stat-label">معدل التحويل</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">🛡️</div>
                  <div className="stat-info">
                    <div className="stat-value">{formatNumber(adStats.totalBlocked)}</div>
                    <div className="stat-label">إعلانات محجوبة</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="monetization-tips">
              <h4>💡 نصائح لزيادة الأرباح:</h4>
              <ul>
                <li>أضف إعلانات جذابة ومتعلقة بالمحتوى</li>
                <li>استخدم روابط تابعة لمنتجات عالية الجودة</li>
                <li>راقب معدلات النقر وحسن الإعلانات</li>
                <li>وزع الإعلانات بشكل متوازن لتجنب إزعاج المستخدمين</li>
                <li>اختبر أنواع مختلفة من الإعلانات</li>
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'ads' && (
          <div className="tab-content">
            <div className="add-ad-section">
              <h4>إضافة محتوى مدعوم جديد</h4>
              <div className="form-group">
                <label>عنوان الإعلان:</label>
                <input
                  type="text"
                  value={newAd.title}
                  onChange={(e) => setNewAd({...newAd, title: e.target.value})}
                  placeholder="مثال: احصل على أفضل VPN"
                />
              </div>
              
              <div className="form-group">
                <label>وصف الإعلان:</label>
                <textarea
                  value={newAd.description}
                  onChange={(e) => setNewAd({...newAd, description: e.target.value})}
                  placeholder="وصف مختصر وجذاب للإعلان"
                  rows="3"
                />
              </div>
              
              <div className="form-group">
                <label>رابط الإعلان:</label>
                <input
                  type="url"
                  value={newAd.link}
                  onChange={(e) => setNewAd({...newAd, link: e.target.value})}
                  placeholder="https://example.com?ref=youtube-player"
                />
              </div>
              
              <div className="form-group">
                <label>نوع الإعلان:</label>
                <select
                  value={newAd.type}
                  onChange={(e) => setNewAd({...newAd, type: e.target.value})}
                >
                  <option value="banner">بانر علوي</option>
                  <option value="sidebar">إعلان جانبي</option>
                </select>
              </div>
              
              <button className="add-btn" onClick={handleAddSponsoredContent}>
                إضافة الإعلان
              </button>
            </div>

            <div className="ad-preview">
              <h4>معاينة الإعلان:</h4>
              {newAd.title && (
                <div className="preview-ad">
                  <div className="ad-content">
                    <div className="ad-text">
                      <h5>{newAd.title}</h5>
                      <p>{newAd.description}</p>
                    </div>
                    <div className="ad-label">إعلان</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'affiliates' && (
          <div className="tab-content">
            <div className="add-affiliate-section">
              <h4>إضافة رابط تابع جديد</h4>
              <div className="form-group">
                <label>اسم الشريك:</label>
                <input
                  type="text"
                  value={newAffiliate.name}
                  onChange={(e) => setNewAffiliate({...newAffiliate, name: e.target.value})}
                  placeholder="مثال: متجر التقنية"
                />
              </div>
              
              <div className="form-group">
                <label>رابط الإحالة:</label>
                <input
                  type="url"
                  value={newAffiliate.url}
                  onChange={(e) => setNewAffiliate({...newAffiliate, url: e.target.value})}
                  placeholder="https://example.com?ref=your-id"
                />
              </div>
              
              <div className="form-group">
                <label>الفئة:</label>
                <input
                  type="text"
                  value={newAffiliate.category}
                  onChange={(e) => setNewAffiliate({...newAffiliate, category: e.target.value})}
                  placeholder="مثال: تقنية، برمجيات، تعليم"
                />
              </div>
              
              <div className="form-group">
                <label>نسبة العمولة (%):</label>
                <input
                  type="number"
                  value={newAffiliate.commission}
                  onChange={(e) => setNewAffiliate({...newAffiliate, commission: parseInt(e.target.value)})}
                  min="1"
                  max="50"
                />
              </div>
              
              <button className="add-btn" onClick={handleAddAffiliateLink}>
                إضافة الرابط التابع
              </button>
            </div>

            <div className="affiliate-info">
              <h4>كيف تعمل الروابط التابعة؟</h4>
              <ul>
                <li>يتم عرض الروابط بشكل طبيعي في التطبيق</li>
                <li>عند نقر المستخدم، يتم توجيهه لموقع الشريك</li>
                <li>تحصل على عمولة عند إتمام عملية شراء</li>
                <li>لا يؤثر على تجربة المستخدم</li>
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="tab-content">
            <div className="monetization-settings">
              <h4>إعدادات الربح</h4>
              
              <div className="setting-item">
                <span>تفعيل الإعلانات البانر</span>
                <div 
                  className={`toggle-switch ${developerMonetization.adConfig.bannerAds ? 'active' : ''}`}
                  onClick={() => {
                    developerMonetization.updateAdConfig({
                      bannerAds: !developerMonetization.adConfig.bannerAds
                    });
                  }}
                />
              </div>

              <div className="setting-item">
                <span>تفعيل الإعلانات الجانبية</span>
                <div 
                  className={`toggle-switch ${developerMonetization.adConfig.sidebarAds ? 'active' : ''}`}
                  onClick={() => {
                    developerMonetization.updateAdConfig({
                      sidebarAds: !developerMonetization.adConfig.sidebarAds
                    });
                  }}
                />
              </div>

              <div className="setting-item">
                <span>تفعيل الروابط التابعة</span>
                <div 
                  className={`toggle-switch ${developerMonetization.affiliateLinks.enabled ? 'active' : ''}`}
                  onClick={() => {
                    developerMonetization.affiliateLinks.enabled = !developerMonetization.affiliateLinks.enabled;
                  }}
                />
              </div>
            </div>

            <div className="export-section">
              <h4>تصدير البيانات</h4>
              <button 
                className="export-btn"
                onClick={() => {
                  const data = {
                    analytics: analytics,
                    adStats: adStats,
                    timestamp: new Date().toISOString()
                  };
                  const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `youtube-player-analytics-${new Date().toISOString().split('T')[0]}.json`;
                  a.click();
                }}
              >
                تصدير الإحصائيات
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeveloperPanel;

