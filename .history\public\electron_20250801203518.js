const { app, BrowserWindow, Menu, ipc<PERSON>ain, session } = require('electron');
const path = require('path');
const isDev = process.env.ELECTRON_IS_DEV === '1';

let mainWindow;

// إعداد مانع الإعلانات المحسن لموقع يوتيوب
const adBlockFilters = [
  // إعلانات Google الأساسية
  '*://*.doubleclick.net/*',
  '*://*.googleadservices.com/*',
  '*://*.googlesyndication.com/*',
  '*://googleads.g.doubleclick.net/*',

  // إعلانات يوتيوب المحددة
  '*://*.youtube.com/api/stats/ads*',
  '*://*.youtube.com/ptracking*',
  '*://*.youtube.com/pagead/*',
  '*://www.youtube.com/api/stats/ads*',
  '*://www.youtube.com/ptracking*',
  '*://www.youtube.com/pagead/*',

  // إعلانات إضافية
  '*://*.googlevideo.com/videoplayback*&adsystem=*',
  '*://youtube.com/get_video_info*&ad_*',
  '*://www.youtube.com/get_video_info*&ad_*',
  '*://*.youtube.com/api/stats/watchtime*',
  '*://www.youtube.com/youtubei/v1/log_event*',

  // شبكات إعلانية أخرى
  '*://*.adsystem.com/*',
  '*://*.adsense.com/*',
  '*://*.amazon-adsystem.com/*',
  '*://*.facebook.com/tr*',
  '*://*.google-analytics.com/*'
];

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مشغل يوتيوب',
    show: false,
    backgroundColor: '#1a1a1a'
  });

  // تحميل موقع يوتيوب مباشرة
  const startUrl = 'https://www.youtube.com';

  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إعداد مانع الإعلانات
  session.defaultSession.webRequest.onBeforeRequest({ urls: adBlockFilters }, (details, callback) => {
    callback({ cancel: true });
  });

  // إضافة فلاتر إضافية لمنع الإعلانات
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ['default-src \'self\' \'unsafe-inline\' \'unsafe-eval\' data: https://www.youtube.com https://youtube.com https://i.ytimg.com https://yt3.ggpht.com;']
      }
    });
  });

  // فتح أدوات المطور في وضع التطوير
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // إزالة شريط القوائم
  Menu.setApplicationMenu(null);

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// هذه الطريقة ستُستدعى عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// الخروج عندما تُغلق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// معالجة الرسائل من العملية المُرسِلة
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

