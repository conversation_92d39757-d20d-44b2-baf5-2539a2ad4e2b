import React from 'react';

const Settings = ({ settings, onSettingsChange, onClose }) => {
  const handleQualityChange = (e) => {
    onSettingsChange({ quality: e.target.value });
  };

  const handleToggle = (setting) => {
    onSettingsChange({ [setting]: !settings[setting] });
  };

  const qualityOptions = [
    { value: 'auto', label: 'تلقائي' },
    { value: 'hd2160', label: '4K (2160p)' },
    { value: 'hd1440', label: '1440p' },
    { value: 'hd1080', label: '1080p' },
    { value: 'hd720', label: '720p' },
    { value: 'large', label: '480p' },
    { value: 'medium', label: '360p' },
    { value: 'small', label: '240p' }
  ];

  return (
    <div className="settings-overlay" onClick={onClose}>
      <div className="settings-modal" onClick={(e) => e.stopPropagation()}>
        <div className="settings-header">
          <h2 className="settings-title">إعدادات التطبيق</h2>
          <button className="close-btn" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="settings-section">
          <h3 className="section-title">إعدادات الفيديو</h3>
          
          <div className="setting-item">
            <span className="setting-label">جودة الفيديو الافتراضية</span>
            <div className="setting-control">
              <select 
                value={settings.quality} 
                onChange={handleQualityChange}
              >
                {qualityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="setting-item">
            <span className="setting-label">إخفاء عناصر التحكم</span>
            <div 
              className={`toggle-switch ${settings.hideControls ? 'active' : ''}`}
              onClick={() => handleToggle('hideControls')}
            />
          </div>
        </div>

        <div className="settings-section">
          <h3 className="section-title">إعدادات التطبيق</h3>
          
          <div className="setting-item">
            <span className="setting-label">مانع الإعلانات</span>
            <div 
              className={`toggle-switch ${settings.adBlock ? 'active' : ''}`}
              onClick={() => handleToggle('adBlock')}
            />
          </div>

          <div className="setting-item">
            <span className="setting-label">الثيم الداكن</span>
            <div 
              className={`toggle-switch ${settings.darkTheme ? 'active' : ''}`}
              onClick={() => handleToggle('darkTheme')}
            />
          </div>
        </div>

        <div className="settings-section">
          <h3 className="section-title">معلومات التطبيق</h3>
          
          <div className="setting-item">
            <span className="setting-label">الإصدار</span>
            <span>1.0.0</span>
          </div>

          <div className="setting-item">
            <span className="setting-label">المطور</span>
            <span>YouTube Player Developer</span>
          </div>
        </div>

        <div style={{ textAlign: 'center', marginTop: '20px', color: '#b3b3b3', fontSize: '12px' }}>
          <p>مشغل يوتيوب مخصص مع واجهة عربية</p>
          <p>جميع الحقوق محفوظة © 2024</p>
        </div>
      </div>
    </div>
  );
};

export default Settings;

