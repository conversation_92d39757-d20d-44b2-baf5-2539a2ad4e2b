{"name": "app-builder-bin", "description": "app-builder precompiled binaries", "version": "5.0.0-alpha.10", "files": ["index.js", "mac", "linux", "win", "index.d.ts"], "license": "MIT", "repository": "develar/app-builder", "keywords": ["snap", "appimage", "icns"], "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "conventional-changelog-cli": "^4.1.0"}, "publishConfig": {"tag": "next", "git-checks": false, "executableFiles": ["linux/arm/app-builder", "linux/arm64/app-builder", "linux/ia32/app-builder", "linux/loong64/app-builder", "linux/riscv64/app-builder", "linux/x64/app-builder", "mac/app-builder", "mac/app-builder_amd64", "mac/app-builder_arm64", "win/arm64/app-builder.exe", "win/ia32/app-builder.exe", "win/x64/app-builder.exe"]}, "scripts": {"changeset": "changeset", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "ci:version": "pnpm changelog && changeset version && make assets && git add .", "ci:publish": "make build-all && pnpm publish --no-git-checks --tag next && changeset tag"}}