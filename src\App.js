import React, { useState, useEffect } from 'react';
import './App.css';
import Header from './components/Header';
import YouTubePlayer from './components/YouTubePlayer';
import Settings from './components/Settings';
import Sidebar from './components/Sidebar';

function App() {
  const [currentVideoId, setCurrentVideoId] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    quality: 'auto',
    hideControls: false,
    adBlock: true,
    darkTheme: true
  });
  const [searchQuery, setSearchQuery] = useState('');

  // استخراج معرف الفيديو من رابط يوتيوب
  const extractVideoId = (url) => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  // معالجة البحث أو تشغيل الفيديو
  const handleVideoLoad = (input) => {
    const videoId = extractVideoId(input);
    if (videoId) {
      setCurrentVideoId(videoId);
    } else {
      // إذا لم يكن رابط، فهو استعلام بحث
      setSearchQuery(input);
    }
  };

  // تحديث الإعدادات
  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  return (
    <div className="app">
      <Header 
        onVideoLoad={handleVideoLoad}
        onSettingsClick={() => setShowSettings(!showSettings)}
      />
      
      <div className="app-content">
        <Sidebar 
          searchQuery={searchQuery}
          onVideoSelect={setCurrentVideoId}
        />
        
        <main className="main-content">
          <YouTubePlayer 
            videoId={currentVideoId}
            settings={settings}
          />
        </main>
        
        {showSettings && (
          <Settings 
            settings={settings}
            onSettingsChange={updateSettings}
            onClose={() => setShowSettings(false)}
          />
        )}
      </div>
    </div>
  );
}

export default App;

