// نظام مانع الإعلانات المتقدم
export class AdBlocker {
  constructor() {
    this.blockedAds = 0;
    this.blockedDomains = new Set();
    this.isEnabled = true;
    this.filters = this.loadFilters();
    this.stats = {
      totalBlocked: 0,
      todayBlocked: 0,
      lastReset: new Date().toDateString()
    };
    
    this.loadStats();
  }

  // قوائم فلترة الإعلانات المحدثة
  loadFilters() {
    return {
      // إعلانات YouTube
      youtube: [
        '*://*.doubleclick.net/*',
        '*://*.googleadservices.com/*',
        '*://*.googlesyndication.com/*',
        '*://googleads.g.doubleclick.net/*',
        '*://*.youtube.com/api/stats/ads*',
        '*://*.youtube.com/ptracking*',
        '*://*.youtube.com/pagead/*',
        '*://www.youtube.com/api/stats/ads*',
        '*://www.youtube.com/ptracking*',
        '*://www.youtube.com/pagead/*',
        '*://*.youtube.com/get_video_info*',
        '*://*.youtube.com/youtubei/v1/player/ad_break*'
      ],
      
      // إعلانات عامة
      general: [
        '*://*.adsystem.com/*',
        '*://*.amazon-adsystem.com/*',
        '*://*.facebook.com/tr*',
        '*://*.google-analytics.com/*',
        '*://*.googletagmanager.com/*',
        '*://*.scorecardresearch.com/*',
        '*://*.outbrain.com/*',
        '*://*.taboola.com/*',
        '*://*.adsafeprotected.com/*',
        '*://*.moatads.com/*'
      ],
      
      // متتبعات
      trackers: [
        '*://*.hotjar.com/*',
        '*://*.fullstory.com/*',
        '*://*.mixpanel.com/*',
        '*://*.segment.com/*',
        '*://*.amplitude.com/*'
      ]
    };
  }

  // تحميل الإحصائيات
  loadStats() {
    const saved = localStorage.getItem('adblock-stats');
    if (saved) {
      try {
        const stats = JSON.parse(saved);
        
        // إعادة تعيين الإحصائيات اليومية إذا تغير اليوم
        if (stats.lastReset !== new Date().toDateString()) {
          stats.todayBlocked = 0;
          stats.lastReset = new Date().toDateString();
        }
        
        this.stats = stats;
      } catch (error) {
        console.error('Error loading ad block stats:', error);
      }
    }
  }

  // حفظ الإحصائيات
  saveStats() {
    localStorage.setItem('adblock-stats', JSON.stringify(this.stats));
  }

  // فحص ما إذا كان الرابط يجب حجبه
  shouldBlock(url) {
    if (!this.isEnabled) return false;

    const allFilters = [
      ...this.filters.youtube,
      ...this.filters.general,
      ...this.filters.trackers
    ];

    return allFilters.some(filter => {
      const regex = new RegExp(filter.replace(/\*/g, '.*'));
      return regex.test(url);
    });
  }

  // حجب الإعلان وتحديث الإحصائيات
  blockAd(url) {
    if (this.shouldBlock(url)) {
      this.blockedAds++;
      this.stats.totalBlocked++;
      this.stats.todayBlocked++;
      
      // استخراج النطاق
      try {
        const domain = new URL(url).hostname;
        this.blockedDomains.add(domain);
      } catch (error) {
        console.error('Error parsing URL:', error);
      }
      
      this.saveStats();
      return true;
    }
    return false;
  }

  // الحصول على الإحصائيات
  getStats() {
    return {
      ...this.stats,
      sessionBlocked: this.blockedAds,
      blockedDomains: Array.from(this.blockedDomains),
      isEnabled: this.isEnabled
    };
  }

  // تفعيل/إلغاء تفعيل مانع الإعلانات
  toggle() {
    this.isEnabled = !this.isEnabled;
    return this.isEnabled;
  }

  // إعادة تعيين الإحصائيات
  resetStats() {
    this.stats = {
      totalBlocked: 0,
      todayBlocked: 0,
      lastReset: new Date().toDateString()
    };
    this.blockedAds = 0;
    this.blockedDomains.clear();
    this.saveStats();
  }

  // إضافة فلتر مخصص
  addCustomFilter(filter) {
    if (!this.filters.custom) {
      this.filters.custom = [];
    }
    this.filters.custom.push(filter);
    localStorage.setItem('custom-filters', JSON.stringify(this.filters.custom));
  }

  // تحميل الفلاتر المخصصة
  loadCustomFilters() {
    const saved = localStorage.getItem('custom-filters');
    if (saved) {
      try {
        this.filters.custom = JSON.parse(saved);
      } catch (error) {
        console.error('Error loading custom filters:', error);
      }
    }
  }
}

// نظام الربح المدمج
export class MonetizationSystem {
  constructor() {
    this.isEnabled = true;
    this.earnings = {
      total: 0,
      today: 0,
      thisMonth: 0,
      lastUpdate: new Date().toDateString()
    };
    
    this.loadEarnings();
    this.startEarningSimulation();
  }

  // تحميل الأرباح
  loadEarnings() {
    const saved = localStorage.getItem('monetization-earnings');
    if (saved) {
      try {
        const earnings = JSON.parse(saved);
        
        // إعادة تعيين الأرباح اليومية إذا تغير اليوم
        if (earnings.lastUpdate !== new Date().toDateString()) {
          earnings.today = 0;
          earnings.lastUpdate = new Date().toDateString();
        }
        
        this.earnings = earnings;
      } catch (error) {
        console.error('Error loading earnings:', error);
      }
    }
  }

  // حفظ الأرباح
  saveEarnings() {
    localStorage.setItem('monetization-earnings', JSON.stringify(this.earnings));
  }

  // محاكاة الأرباح (في التطبيق الحقيقي ستكون مرتبطة بخدمة حقيقية)
  startEarningSimulation() {
    if (!this.isEnabled) return;

    // ربح صغير كل دقيقة (محاكاة)
    setInterval(() => {
      const earning = Math.random() * 0.001; // ربح عشوائي صغير
      this.addEarning(earning);
    }, 60000); // كل دقيقة

    // ربح إضافي عند مشاهدة الفيديوهات
    this.setupVideoWatchingRewards();
  }

  // إضافة ربح
  addEarning(amount) {
    this.earnings.total += amount;
    this.earnings.today += amount;
    this.earnings.thisMonth += amount;
    this.saveEarnings();
  }

  // إعداد مكافآت مشاهدة الفيديوهات
  setupVideoWatchingRewards() {
    // مكافأة عند تشغيل فيديو جديد
    window.addEventListener('video-started', () => {
      const reward = Math.random() * 0.005;
      this.addEarning(reward);
    });

    // مكافأة عند إكمال مشاهدة فيديو
    window.addEventListener('video-completed', () => {
      const reward = Math.random() * 0.01;
      this.addEarning(reward);
    });
  }

  // الحصول على الأرباح
  getEarnings() {
    return {
      ...this.earnings,
      formatted: {
        total: this.formatCurrency(this.earnings.total),
        today: this.formatCurrency(this.earnings.today),
        thisMonth: this.formatCurrency(this.earnings.thisMonth)
      }
    };
  }

  // تنسيق العملة
  formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  }

  // تفعيل/إلغاء تفعيل نظام الربح
  toggle() {
    this.isEnabled = !this.isEnabled;
    if (this.isEnabled) {
      this.startEarningSimulation();
    }
    return this.isEnabled;
  }

  // سحب الأرباح (محاكاة)
  withdraw(amount) {
    if (amount <= this.earnings.total && amount >= 1.0) {
      this.earnings.total -= amount;
      this.saveEarnings();
      return {
        success: true,
        message: `تم سحب ${this.formatCurrency(amount)} بنجاح`,
        newBalance: this.formatCurrency(this.earnings.total)
      };
    } else {
      return {
        success: false,
        message: 'المبلغ غير صالح أو الرصيد غير كافي (الحد الأدنى للسحب $1.00)',
        newBalance: this.formatCurrency(this.earnings.total)
      };
    }
  }
}

// إنشاء مثيلات عامة
export const adBlocker = new AdBlocker();
export const monetization = new MonetizationSystem();

